// Performance Optimization Module
// Handles critical performance optimizations for the Naroop platform

class PerformanceOptimizer {
    constructor() {
        this.metrics = {
            loadTime: 0,
            renderTime: 0,
            interactionTime: 0,
            memoryUsage: 0,
            networkRequests: 0
        };
        
        this.observers = {
            intersection: null,
            performance: null,
            mutation: null
        };
        
        this.cache = new Map();
        this.imageCache = new Map();
        this.requestQueue = [];
        this.isInitialized = false;
        
        // Performance thresholds
        this.thresholds = {
            criticalRenderTime: 1000, // 1 second
            maxMemoryUsage: 50 * 1024 * 1024, // 50MB
            maxConcurrentRequests: 6,
            imageLoadTimeout: 5000 // 5 seconds
        };
    }

    // Initialize performance optimization
    async init() {
        try {
            console.log('🚀 Initializing Performance Optimizer...');
            
            // Start performance monitoring
            this.startPerformanceMonitoring();
            
            // Initialize lazy loading
            this.initializeLazyLoading();
            
            // Optimize images
            this.optimizeImages();
            
            // Initialize request batching
            this.initializeRequestBatching();
            
            // Preload critical resources
            await this.preloadCriticalResources();
            
            // Initialize memory management
            this.initializeMemoryManagement();
            
            // Setup performance observers
            this.setupPerformanceObservers();
            
            this.isInitialized = true;
            console.log('✅ Performance Optimizer initialized successfully');
            
            // Report initial metrics
            this.reportMetrics();
            
        } catch (error) {
            console.error('❌ Error initializing Performance Optimizer:', error);
            throw error;
        }
    }

    // Start performance monitoring
    startPerformanceMonitoring() {
        const startTime = performance.now();
        
        // Monitor page load performance
        window.addEventListener('load', () => {
            this.metrics.loadTime = performance.now() - startTime;
            console.log(`📊 Page load time: ${this.metrics.loadTime.toFixed(2)}ms`);
            
            // Check if load time exceeds threshold
            if (this.metrics.loadTime > this.thresholds.criticalRenderTime) {
                console.warn(`⚠️ Page load time exceeds threshold: ${this.metrics.loadTime.toFixed(2)}ms`);
                this.optimizeForSlowLoading();
            }
        });
        
        // Monitor DOM content loaded
        document.addEventListener('DOMContentLoaded', () => {
            this.metrics.renderTime = performance.now() - startTime;
            console.log(`📊 DOM render time: ${this.metrics.renderTime.toFixed(2)}ms`);
        });
        
        // Monitor first interaction
        const interactionEvents = ['click', 'keydown', 'touchstart'];
        const recordFirstInteraction = () => {
            this.metrics.interactionTime = performance.now() - startTime;
            console.log(`📊 First interaction time: ${this.metrics.interactionTime.toFixed(2)}ms`);
            
            // Remove listeners after first interaction
            interactionEvents.forEach(event => {
                document.removeEventListener(event, recordFirstInteraction);
            });
        };
        
        interactionEvents.forEach(event => {
            document.addEventListener(event, recordFirstInteraction, { once: true });
        });
    }

    // Initialize lazy loading for images and content
    initializeLazyLoading() {
        // Create intersection observer for lazy loading
        if ('IntersectionObserver' in window) {
            this.observers.intersection = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadLazyElement(entry.target);
                        this.observers.intersection.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px', // Start loading 50px before element is visible
                threshold: 0.1
            });
            
            // Observe all lazy-loadable elements
            this.observeLazyElements();
            
            console.log('✅ Lazy loading initialized');
        } else {
            console.warn('⚠️ IntersectionObserver not supported, falling back to immediate loading');
            this.loadAllLazyElements();
        }
    }

    // Observe elements for lazy loading
    observeLazyElements() {
        // Images with data-src attribute
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            this.observers.intersection.observe(img);
        });
        
        // Content sections with data-lazy attribute
        const lazyContent = document.querySelectorAll('[data-lazy]');
        lazyContent.forEach(element => {
            this.observers.intersection.observe(element);
        });
        
        // Post content that can be loaded on demand
        const lazyPosts = document.querySelectorAll('.post-item[data-post-id]');
        lazyPosts.forEach(post => {
            if (!post.querySelector('.post-content')) {
                this.observers.intersection.observe(post);
            }
        });
    }

    // Load lazy element when it becomes visible
    async loadLazyElement(element) {
        try {
            if (element.tagName === 'IMG' && element.dataset.src) {
                await this.loadLazyImage(element);
            } else if (element.dataset.lazy) {
                await this.loadLazyContent(element);
            } else if (element.classList.contains('post-item')) {
                await this.loadLazyPost(element);
            }
        } catch (error) {
            console.error('Error loading lazy element:', error);
        }
    }

    // Load lazy image with optimization
    async loadLazyImage(img) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Image load timeout'));
            }, this.thresholds.imageLoadTimeout);
            
            const tempImg = new Image();
            
            tempImg.onload = () => {
                clearTimeout(timeout);
                
                // Apply image optimizations
                this.optimizeImageElement(img, tempImg);
                
                // Set the source and add fade-in effect
                img.src = tempImg.src;
                img.classList.add('lazy-loaded');
                
                // Cache the image
                this.imageCache.set(img.dataset.src, tempImg.src);
                
                resolve();
            };
            
            tempImg.onerror = () => {
                clearTimeout(timeout);
                console.error('Failed to load image:', img.dataset.src);
                
                // Set fallback image
                img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIE5vdCBBdmFpbGFibGU8L3RleHQ+PC9zdmc+';
                img.classList.add('lazy-error');
                
                reject(new Error('Image load failed'));
            };
            
            // Check cache first
            if (this.imageCache.has(img.dataset.src)) {
                tempImg.src = this.imageCache.get(img.dataset.src);
            } else {
                tempImg.src = img.dataset.src;
            }
        });
    }

    // Optimize image element
    optimizeImageElement(img, loadedImg) {
        // Set optimal dimensions
        if (loadedImg.naturalWidth > 800) {
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
        }
        
        // Add loading attribute for native lazy loading support
        img.loading = 'lazy';
        
        // Add decode attribute for better performance
        img.decoding = 'async';
    }

    // Optimize all images on the page
    optimizeImages() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            // Add native lazy loading
            if (!img.loading) {
                img.loading = 'lazy';
            }
            
            // Add async decoding
            if (!img.decoding) {
                img.decoding = 'async';
            }
            
            // Convert to lazy loading if not already
            if (img.src && !img.dataset.src) {
                img.dataset.src = img.src;
                img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InRyYW5zcGFyZW50Ii8+PC9zdmc+';
                
                if (this.observers.intersection) {
                    this.observers.intersection.observe(img);
                }
            }
        });
        
        console.log(`✅ Optimized ${images.length} images for lazy loading`);
    }

    // Initialize request batching for better network performance
    initializeRequestBatching() {
        this.requestBatcher = {
            queue: [],
            processing: false,
            batchSize: 5,
            batchDelay: 100 // ms
        };
        
        console.log('✅ Request batching initialized');
    }

    // Batch API requests for better performance
    async batchRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            this.requestBatcher.queue.push({
                url,
                options,
                resolve,
                reject,
                timestamp: Date.now()
            });
            
            this.processBatchQueue();
        });
    }

    // Process batched requests
    async processBatchQueue() {
        if (this.requestBatcher.processing || this.requestBatcher.queue.length === 0) {
            return;
        }
        
        this.requestBatcher.processing = true;
        
        // Wait for batch delay to collect more requests
        await new Promise(resolve => setTimeout(resolve, this.requestBatcher.batchDelay));
        
        const batch = this.requestBatcher.queue.splice(0, this.requestBatcher.batchSize);
        
        // Process batch concurrently
        const promises = batch.map(async (request) => {
            try {
                const response = await fetch(request.url, request.options);
                const data = await response.json();
                request.resolve(data);
            } catch (error) {
                request.reject(error);
            }
        });
        
        await Promise.allSettled(promises);
        
        this.requestBatcher.processing = false;
        
        // Process remaining queue
        if (this.requestBatcher.queue.length > 0) {
            this.processBatchQueue();
        }
    }

    // Preload critical resources
    async preloadCriticalResources() {
        const criticalResources = [
            '/api/user/profile',
            '/api/posts/recent',
            'public/css/main.css',
            'public/js/core.js'
        ];
        
        const preloadPromises = criticalResources.map(resource => {
            return new Promise((resolve) => {
                const link = document.createElement('link');
                link.rel = 'preload';
                
                if (resource.endsWith('.css')) {
                    link.as = 'style';
                } else if (resource.endsWith('.js')) {
                    link.as = 'script';
                } else {
                    link.as = 'fetch';
                    link.crossOrigin = 'anonymous';
                }
                
                link.href = resource;
                link.onload = resolve;
                link.onerror = resolve; // Don't fail if preload fails
                
                document.head.appendChild(link);
            });
        });
        
        await Promise.allSettled(preloadPromises);
        console.log('✅ Critical resources preloaded');
    }

    // Initialize memory management
    initializeMemoryManagement() {
        // Monitor memory usage
        if ('memory' in performance) {
            setInterval(() => {
                const memInfo = performance.memory;
                this.metrics.memoryUsage = memInfo.usedJSHeapSize;

                // Check if memory usage exceeds threshold
                if (memInfo.usedJSHeapSize > this.thresholds.maxMemoryUsage) {
                    console.warn(`⚠️ High memory usage: ${(memInfo.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
                    this.performMemoryCleanup();
                }
            }, 30000); // Check every 30 seconds
        }

        // Clean up on page unload
        window.addEventListener('beforeunload', () => {
            this.performMemoryCleanup();
        });

        console.log('✅ Memory management initialized');
    }

    // Perform memory cleanup
    performMemoryCleanup() {
        // Clear caches
        this.cache.clear();

        // Clear old image cache entries
        if (this.imageCache.size > 50) {
            const entries = Array.from(this.imageCache.entries());
            const toDelete = entries.slice(0, entries.length - 25);
            toDelete.forEach(([key]) => this.imageCache.delete(key));
        }

        // Clear request queue
        this.requestQueue = [];

        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }

        console.log('🧹 Memory cleanup performed');
    }

    // Setup performance observers
    setupPerformanceObservers() {
        // Performance Observer for navigation timing
        if ('PerformanceObserver' in window) {
            try {
                this.observers.performance = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (entry.entryType === 'navigation') {
                            console.log(`📊 Navigation timing: ${entry.loadEventEnd - entry.loadEventStart}ms`);
                        } else if (entry.entryType === 'resource') {
                            this.metrics.networkRequests++;

                            // Log slow resources
                            if (entry.duration > 1000) {
                                console.warn(`⚠️ Slow resource: ${entry.name} (${entry.duration.toFixed(2)}ms)`);
                            }
                        }
                    });
                });

                this.observers.performance.observe({ entryTypes: ['navigation', 'resource'] });
                console.log('✅ Performance observers initialized');

            } catch (error) {
                console.warn('⚠️ Performance Observer not fully supported:', error);
            }
        }
    }

    // Load lazy content
    async loadLazyContent(element) {
        const contentType = element.dataset.lazy;

        try {
            switch (contentType) {
                case 'posts':
                    await this.loadLazyPosts(element);
                    break;
                case 'comments':
                    await this.loadLazyComments(element);
                    break;
                case 'profile':
                    await this.loadLazyProfile(element);
                    break;
                default:
                    console.warn(`Unknown lazy content type: ${contentType}`);
            }
        } catch (error) {
            console.error(`Error loading lazy content (${contentType}):`, error);
        }
    }

    // Load lazy post content
    async loadLazyPost(postElement) {
        const postId = postElement.dataset.postId;
        if (!postId) return;

        try {
            // Show loading state
            postElement.classList.add('loading');

            // Fetch post content
            const response = await this.batchRequest(`/api/posts/${postId}`);

            // Update post content
            const contentContainer = postElement.querySelector('.post-content') ||
                                   document.createElement('div');
            contentContainer.className = 'post-content';
            contentContainer.innerHTML = this.sanitizeHTML(response.content);

            if (!postElement.querySelector('.post-content')) {
                postElement.appendChild(contentContainer);
            }

            // Remove loading state
            postElement.classList.remove('loading');
            postElement.classList.add('loaded');

        } catch (error) {
            console.error(`Error loading post ${postId}:`, error);
            postElement.classList.remove('loading');
            postElement.classList.add('error');
        }
    }

    // Optimize for slow loading conditions
    optimizeForSlowLoading() {
        console.log('🐌 Optimizing for slow loading conditions...');

        // Reduce image quality
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            if (img.dataset.src && !img.dataset.lowQuality) {
                img.dataset.lowQuality = img.dataset.src;
                // Add low quality parameter if it's an API endpoint
                if (img.dataset.src.includes('/api/')) {
                    img.dataset.src += '?quality=low';
                }
            }
        });

        // Disable non-essential animations
        document.body.classList.add('reduced-motion');

        // Increase lazy loading threshold
        if (this.observers.intersection) {
            this.observers.intersection.disconnect();

            this.observers.intersection = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadLazyElement(entry.target);
                        this.observers.intersection.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '20px 0px', // Reduced from 50px
                threshold: 0.25 // Increased threshold
            });

            this.observeLazyElements();
        }

        console.log('✅ Slow loading optimizations applied');
    }

    // Sanitize HTML content
    sanitizeHTML(html) {
        const div = document.createElement('div');
        div.textContent = html;
        return div.innerHTML;
    }

    // Report performance metrics
    reportMetrics() {
        const metrics = {
            ...this.metrics,
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            connection: navigator.connection ? {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt
            } : null
        };

        console.log('📊 Performance Metrics:', metrics);

        // Send metrics to server for analysis (if endpoint exists)
        if (typeof fetch !== 'undefined') {
            fetch('/api/metrics/performance', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(metrics)
            }).catch(error => {
                console.warn('Could not send performance metrics:', error);
            });
        }

        return metrics;
    }

    // Get current performance metrics
    getMetrics() {
        return { ...this.metrics };
    }

    // Check if performance optimization is initialized
    isReady() {
        return this.isInitialized;
    }

    // Destroy performance optimizer and clean up
    destroy() {
        // Disconnect observers
        if (this.observers.intersection) {
            this.observers.intersection.disconnect();
        }

        if (this.observers.performance) {
            this.observers.performance.disconnect();
        }

        if (this.observers.mutation) {
            this.observers.mutation.disconnect();
        }

        // Clear caches
        this.cache.clear();
        this.imageCache.clear();

        // Clear request queue
        this.requestQueue = [];

        this.isInitialized = false;
        console.log('🧹 Performance Optimizer destroyed');
    }
}

// Create global instance
const performanceOptimizer = new PerformanceOptimizer();

// Make available globally
window.performanceOptimizer = performanceOptimizer;

// Export for module usage
export default performanceOptimizer;
