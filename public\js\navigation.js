// Enhanced Navigation system for Naroop
import { CoreUtils } from './core.js';

console.log('🧭 Loading enhanced navigation system...');

export const Navigation = {
    currentSection: 'feed',
    isInitialized: false,
    navigationHistory: [],

    // Initialize navigation system
    init() {
        console.log('🔧 Initializing enhanced navigation...');

        if (this.isInitialized) {
            console.warn('Navigation already initialized');
            return;
        }

        this.setupNavigationListeners();
        this.setupAdditionalFeatures();
        this.initializeWithCorrectSection();
        this.isInitialized = true;

        console.log('✅ Enhanced navigation initialized successfully');
    },

    // Switch to a specific section with enhanced error handling and history
    switchToSection(sectionName) {
        try {
            console.log('🔄 Switching to section:', sectionName);

            // Validate section name
            if (!sectionName || typeof sectionName !== 'string') {
                console.error('❌ Invalid section name:', sectionName);
                return false;
            }

            // Check if already on this section
            if (this.currentSection === sectionName) {
                console.log('ℹ️ Already on section:', sectionName);
                return true;
            }

            // Hide all sections
            const allSections = document.querySelectorAll('.content-section');
            console.log(`Found ${allSections.length} sections to hide`);
            allSections.forEach(section => {
                console.log(`Hiding section: ${section.id}`);
                section.classList.remove('active');
            });

            // Show target section
            const targetSection = document.getElementById(`${sectionName}-section`);
            console.log(`Looking for section: ${sectionName}-section`);
            console.log(`Target section found:`, targetSection);
            if (targetSection) {
                // Add to navigation history
                this.addToHistory(sectionName);

                // Update current section
                const previousSection = this.currentSection;
                this.currentSection = sectionName;

                // Show section immediately
                targetSection.classList.add('active');
                console.log(`✅ Switched from ${previousSection} to ${sectionName}`);
                console.log(`Target section classes after adding active:`, targetSection.className);
            } else {
                console.error('❌ Section not found:', sectionName);
                // Try to fallback to feed section
                if (sectionName !== 'feed') {
                    console.log('🔄 Falling back to feed section');
                    return this.switchToSection('feed');
                }
                return false;
            }

            // Update navigation active states
            this.updateNavigationStates(sectionName);

            // Update URL hash
            if (window.location.hash !== `#${sectionName}`) {
                window.history.pushState(null, null, `#${sectionName}`);
            }

            // Handle section-specific initialization
            this.handleSectionChange(sectionName);

            // Dispatch custom event for other modules
            window.dispatchEvent(new CustomEvent('sectionChanged', {
                detail: {
                    section: sectionName,
                    previousSection: this.navigationHistory.length > 1 ?
                        this.navigationHistory[this.navigationHistory.length - 2].section : null
                }
            }));

            return true;
        } catch (error) {
            console.error('❌ Error switching to section:', error);
            return false;
        }
    },

    // Update navigation button states
    updateNavigationStates(activeSectionName) {
        // Update navigation items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.section === activeSectionName) {
                item.classList.add('active');
            }
        });

        // Update mobile navigation
        document.querySelectorAll('.mobile-nav-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.section === activeSectionName) {
                item.classList.add('active');
            }
        });
    },

    // Setup navigation event listeners with enhanced error handling
    setupNavigationListeners() {
        try {
            // Navigation items with improved event handling
            document.querySelectorAll('.nav-item').forEach((item, index) => {
                if (!item.dataset.section) {
                    console.warn(`Navigation item ${index} missing data-section attribute`);
                    return;
                }

                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    const sectionName = item.dataset.section;
                    console.log(`🔄 Navigation clicked! Section: ${sectionName}`);
                    console.log('Item clicked:', item);
                    console.log('Dataset:', item.dataset);

                    if (this.switchToSection(sectionName)) {
                        // Add visual feedback
                        this.addNavigationFeedback(item);
                    } else {
                        console.error(`❌ Failed to switch to section: ${sectionName}`);
                    }
                });

                // Add keyboard navigation support
                item.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        item.click();
                    }
                });

                // Ensure proper accessibility
                if (!item.getAttribute('role')) {
                    item.setAttribute('role', 'button');
                }
                if (!item.getAttribute('tabindex')) {
                    item.setAttribute('tabindex', '0');
                }
            });

            // Mobile navigation with enhanced support
            document.querySelectorAll('.mobile-nav-item').forEach((item, index) => {
                if (!item.dataset.section) {
                    console.warn(`Mobile nav item ${index} missing data-section attribute`);
                    return;
                }

                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    const sectionName = item.dataset.section;
                    console.log(`📱 Mobile navigation to: ${sectionName}`);

                    if (this.switchToSection(sectionName)) {
                        this.addNavigationFeedback(item);
                    }
                });

                // Mobile-specific touch handling
                item.addEventListener('touchstart', (e) => {
                    item.classList.add('touch-active');
                }, { passive: true });

                item.addEventListener('touchend', (e) => {
                    setTimeout(() => {
                        item.classList.remove('touch-active');
                    }, 150);
                }, { passive: true });
            });

            // Enhanced browser navigation handling
            window.addEventListener('popstate', (e) => {
                console.log('🔙 Browser navigation detected');
                this.handleInitialHash();
            });

            console.log('✅ Navigation listeners setup complete');
        } catch (error) {
            console.error('❌ Error setting up navigation listeners:', error);
        }
    },

    // Initialize with correct section based on URL hash
    initializeWithCorrectSection() {
        this.handleInitialHash();
    },

    // Handle initial hash or default to feed
    handleInitialHash() {
        const hash = window.location.hash.substring(1);
        const initialSection = (hash && document.getElementById(`${hash}-section`)) ? hash : 'feed';
        this.switchToSection(initialSection);
    },

    // Handle section-specific initialization
    handleSectionChange(sectionName) {
        switch (sectionName) {
            case 'feed':
                // Initialize feed if needed
                if (window.Posts && typeof window.Posts.loadPosts === 'function') {
                    window.Posts.loadPosts();
                }
                break;
            case 'profile':
                // Initialize profile if needed
                if (window.Profile && typeof window.Profile.loadUserProfile === 'function') {
                    window.Profile.loadUserProfile();
                }
                break;
            case 'explore':
                // Initialize explore section
                this.initializeExploreSection();
                break;
            case 'messages':
                // Initialize messages section
                this.initializeMessagesSection();
                break;
            case 'settings':
                // Initialize settings section
                this.initializeSettingsSection();
                break;
        }
    },

    // Initialize explore section
    initializeExploreSection() {
        console.log('🔍 Initializing explore section...');
        // Add explore-specific functionality here
    },

    // Initialize messages section
    initializeMessagesSection() {
        console.log('💬 Initializing messages section...');
        // Add messages-specific functionality here
    },

    // Initialize settings section
    initializeSettingsSection() {
        console.log('⚙️ Initializing settings section...');
        // Add settings-specific functionality here
    },

    // Get current section
    getCurrentSection() {
        return this.currentSection;
    },

    // Check if section exists
    sectionExists(sectionName) {
        return document.getElementById(`${sectionName}-section`) !== null;
    },

    // Setup additional navigation features
    setupAdditionalFeatures() {
        try {
            // Refresh button functionality
            const refreshBtn = document.querySelector('.refresh-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('🔄 Refreshing current section...');

                    // Add refresh animation
                    refreshBtn.classList.add('rotating');
                    // Reset animation
                    setTimeout(() => {
                        refreshBtn.classList.remove('rotating');
                    }, 500);

                    // Refresh current section content
                    this.refreshCurrentSection();
                });
            }

            // Load more button functionality
            const loadMoreBtn = document.querySelector('.load-more');
            if (loadMoreBtn) {
                loadMoreBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('📄 Loading more content...');

                    const originalText = loadMoreBtn.textContent;
                    loadMoreBtn.textContent = 'Loading...';
                    loadMoreBtn.disabled = true;

                    // Simulate loading (replace with actual loading logic)
                    setTimeout(() => {
                        loadMoreBtn.textContent = originalText;
                        loadMoreBtn.disabled = false;

                        // Trigger load more for current section
                        this.loadMoreContent();
                    }, 1000);
                });
            }

            // Trending items functionality
            const trendingItems = document.querySelectorAll('.trending-item');
            trendingItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    const topicElement = item.querySelector('.trending-topic');
                    if (topicElement) {
                        const topic = topicElement.textContent;
                        console.log('🔥 Clicked trending topic:', topic);
                        this.handleTrendingTopicClick(topic);
                    }
                });
            });

            console.log('✅ Additional navigation features setup complete');
        } catch (error) {
            console.error('❌ Error setting up additional features:', error);
        }
    },

    // Add visual feedback for navigation
    addNavigationFeedback(element) {
        element.classList.add('nav-clicked');
        setTimeout(() => {
            element.classList.remove('nav-clicked');
        }, 200);
    },

    // Refresh current section content
    refreshCurrentSection() {
        switch (this.currentSection) {
            case 'feed':
                if (window.Posts && typeof window.Posts.loadPosts === 'function') {
                    window.Posts.loadPosts();
                }
                break;
            case 'profile':
                if (window.Profile && typeof window.Profile.loadUserProfile === 'function') {
                    window.Profile.loadUserProfile();
                }
                break;
            default:
                console.log(`Refresh not implemented for section: ${this.currentSection}`);
        }
    },

    // Load more content for current section
    loadMoreContent() {
        switch (this.currentSection) {
            case 'feed':
                if (window.Posts && typeof window.Posts.loadMorePosts === 'function') {
                    window.Posts.loadMorePosts();
                } else {
                    console.log('Load more posts functionality not available');
                }
                break;
            default:
                console.log(`Load more not implemented for section: ${this.currentSection}`);
        }
    },

    // Handle trending topic clicks
    handleTrendingTopicClick(topic) {
        // Switch to explore section and search for the topic
        this.switchToSection('explore');

        // If search functionality is available, trigger search
        if (window.Search && typeof window.Search.searchTopic === 'function') {
            window.Search.searchTopic(topic);
        } else {
            console.log(`Would search for topic: ${topic}`);
        }
    },

    // Add navigation to history
    addToHistory(section) {
        this.navigationHistory.push({
            section: section,
            timestamp: Date.now(),
            url: window.location.href
        });

        // Keep only last 10 navigation entries
        if (this.navigationHistory.length > 10) {
            this.navigationHistory.shift();
        }
    },

    // Get navigation history
    getNavigationHistory() {
        return this.navigationHistory;
    }
};

// Initialize navigation when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('🔧 DOM loaded, initializing navigation...');

    // Check if navigation elements exist
    const navItems = document.querySelectorAll('.nav-item');
    const contentSections = document.querySelectorAll('.content-section');

    console.log(`Found ${navItems.length} navigation items`);
    console.log(`Found ${contentSections.length} content sections`);

    // Wait a bit for other modules to load
    setTimeout(() => {
        Navigation.init();
    }, 100);
});

// Make navigation available globally
window.Navigation = Navigation;
