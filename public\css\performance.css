/* Performance Optimization CSS
   Critical performance styles for the Naroop platform */

/* Critical rendering optimizations */
* {
    box-sizing: border-box;
}

/* Optimize font loading */
@font-face {
    font-family: 'System';
    src: local('system-ui'), local('-apple-system'), local('BlinkMacSystemFont'), 
         local('Segoe UI'), local('Roboto'), local('Oxygen'), local('Ubuntu'), 
         local('Cantarell'), local('Helvetica Neue'), local('sans-serif');
    font-display: swap;
}

/* Use system fonts for better performance */
body {
    font-family: 'System', -apple-system, BlinkMacSystemFont, 'Segoe UI', 
                 Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    font-display: swap;
}

/* Optimize animations for performance */
* {
    will-change: auto;
}

/* Only animate transform and opacity for better performance */
.animate-transform {
    will-change: transform;
    transform: translateZ(0); /* Force hardware acceleration */
}

.animate-opacity {
    will-change: opacity;
}

/* Lazy loading styles */
img[data-src] {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
}

img.lazy-loaded {
    opacity: 1;
    background: none;
    animation: none;
}

img.lazy-error {
    opacity: 0.5;
    background: #f5f5f5;
    animation: none;
}

/* Loading shimmer animation */
@keyframes loading-shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Optimize scrolling performance */
.scroll-container {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    contain: layout style paint;
}

/* Optimize list rendering */
.post-list {
    contain: layout style paint;
}

.post-item {
    contain: layout style;
    transform: translateZ(0); /* Force layer creation */
}

/* Loading states */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 25%, rgba(255,255,255,0.5) 50%, transparent 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
    z-index: 1;
}

/* Optimize form performance */
.form-field {
    contain: layout style;
}

/* Reduce paint on hover for better performance */
.hover-optimize {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-optimize:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Optimize button interactions */
.btn-optimized {
    transform: translateZ(0);
    transition: transform 0.15s ease;
}

.btn-optimized:active {
    transform: translateZ(0) scale(0.98);
}

/* Critical above-the-fold styles */
.header {
    contain: layout style paint;
    will-change: transform;
}

.sidebar {
    contain: layout style paint;
    transform: translateZ(0);
}

.main-content {
    contain: layout style paint;
}

/* Optimize image containers */
.image-container {
    position: relative;
    overflow: hidden;
    background: #f5f5f5;
}

.image-container img {
    width: 100%;
    height: auto;
    display: block;
}

/* Optimize modal performance */
.modal {
    contain: layout style paint;
    transform: translateZ(0);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .loading::before {
        animation: none;
        background: rgba(255,255,255,0.5);
    }
    
    img[data-src] {
        animation: none;
        background: #f0f0f0;
    }
}

/* High contrast mode optimizations */
@media (prefers-contrast: high) {
    .loading::before {
        background: linear-gradient(90deg, transparent 25%, rgba(0,0,0,0.3) 50%, transparent 75%);
    }
    
    img[data-src] {
        background: #e0e0e0;
    }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
    img[data-src] {
        background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    }
    
    .loading::before {
        background: linear-gradient(90deg, transparent 25%, rgba(255,255,255,0.1) 50%, transparent 75%);
    }
    
    .image-container {
        background: #2a2a2a;
    }
}

/* Print optimizations */
@media print {
    * {
        animation: none !important;
        transition: none !important;
        background: white !important;
        color: black !important;
    }
    
    .loading::before {
        display: none;
    }
    
    img[data-src] {
        opacity: 1;
        background: none;
    }
}

/* Performance utilities */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

.contain-layout {
    contain: layout;
}

.contain-style {
    contain: style;
}

.contain-paint {
    contain: paint;
}

.contain-all {
    contain: layout style paint;
}

/* Optimize text rendering */
.optimize-text {
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Critical path CSS - inline these styles */
.critical-header {
    background: #8B4513;
    color: white;
    padding: 1rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.critical-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.critical-logo {
    font-size: 1.5rem;
    font-weight: bold;
}

/* Responsive performance optimizations */
@media (max-width: 768px) {
    /* Reduce complexity on mobile */
    .hover-optimize:hover {
        transform: none;
        box-shadow: none;
    }
    
    /* Optimize touch interactions */
    .touch-optimized {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }
    
    /* Reduce animations on mobile */
    .mobile-reduce-motion {
        animation-duration: 0.2s !important;
        transition-duration: 0.2s !important;
    }
}

/* Very small screens - minimal animations */
@media (max-width: 480px) {
    * {
        animation-duration: 0.1s !important;
        transition-duration: 0.1s !important;
    }
    
    .loading::before {
        animation-duration: 1s;
    }
}
