/* Main Styles for Naroop Social Media Platform */

/* ===== COMPONENT BASE CLASSES ===== */

/* Card Base - Consistent styling for all card-like components */
.card-base {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    border: var(--card-border);
    backdrop-filter: var(--card-backdrop-blur);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.card-base:hover {
    background: var(--bg-card-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Card Padding Variants */
.card-padding {
    padding: var(--card-padding);
}

.card-padding-sm {
    padding: var(--spacing-lg);
}

.card-padding-lg {
    padding: var(--spacing-3xl);
}

/* Card Positioning */
.card-sticky {
    position: sticky;
    top: 110px;
    height: fit-content;
}

/* Button Base Classes */
.btn-base {
    border: none;
    border-radius: var(--radius-xl);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: var(--transition-normal);
    font-family: var(--font-family-primary);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.btn-primary {
    background: var(--gradient-button);
    color: var(--text-primary);
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    background: var(--gradient-button-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-strong);
}

.btn-sm {
    padding: var(--button-padding-sm);
    font-size: var(--font-size-sm);
}

.btn-md {
    padding: var(--button-padding-md);
    font-size: var(--font-size-base);
}

.btn-lg {
    padding: var(--button-padding-lg);
    font-size: var(--font-size-lg);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth; /* Enable smooth scrolling for anchor links */
}

body {
    font-family: var(--font-family-primary);
    background: var(--bg-primary);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
}

/* Container and Layout */
.container {
    max-width: 100vw;
    margin: 0 auto;
    padding: var(--spacing-md);
    min-height: 100vh;
}

/* Grid and Flexbox Layouts */
.card-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
}

.flex-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: 0 8px 32px var(--color-shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 100;
}

.logo {
    font-size: 32px;
    font-weight: 700;
    background: linear-gradient(45deg, var(--color-primary), var(--color-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

/* Navigation Buttons */
.nav-buttons {
    display: flex;
    gap: 15px;
    align-items: center;
}

.nav-btn {
    background: transparent;
    border: 2px solid #8B4513;
    color: #8B4513;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-size: 14px;
}

.nav-btn:hover {
    background: #8B4513;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(139, 69, 19, 0.3);
}

.nav-btn.primary {
    background: #8B4513;
    color: white;
}

.nav-btn.primary:hover {
    background: #7a3c0f;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(139, 69, 19, 0.4);
}

/* Main Content Layout */
.main-content {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 24px;
    align-items: start;
    min-height: calc(100vh - 140px);
    padding: 0 20px;
}

/* Sidebar Styles */
.sidebar {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 20px;
}

.sidebar h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
    font-weight: 700;
}

.sidebar-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 10px;
    margin-bottom: 8px;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    position: relative;
    overflow: hidden;
}

.sidebar-item:hover {
    background: rgba(139, 69, 19, 0.1);
    transform: translateX(5px);
}

.sidebar-item.active {
    background: rgba(139, 69, 19, 0.15);
    color: #8B4513;
    font-weight: 600;
}

.sidebar-item.nav-clicked {
    background: rgba(139, 69, 19, 0.25);
    transform: translateX(8px);
}

.sidebar-item::before {
    content: "📱";
    margin-right: 12px;
    font-size: 16px;
    transition: transform 0.3s ease;
}

.sidebar-item:hover::before {
    transform: scale(1.2);
}

/* Sidebar Icons */
.sidebar-item:nth-child(2)::before { content: "🏠"; }
.sidebar-item:nth-child(3)::before { content: "🔍"; }
.sidebar-item:nth-child(4)::before { content: "💬"; }
.sidebar-item:nth-child(5)::before { content: "👤"; }

/* Feed Section */
.feed-section {
    background: #fafafa;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
    max-width: 600px;
    margin: 0 auto;
}

.feed-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.feed-title {
    font-size: 28px;
    font-weight: 800;
    color: #1a1a1a;
    background: linear-gradient(135deg, #8B4513, #D2691E);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Refresh Button */
.refresh-btn {
    background: white;
    color: #6b7280;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 10px 16px;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.refresh-btn:hover {
    background: #f9fafb;
    color: #374151;
    border-color: rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.refresh-btn::before {
    content: '🔄';
    font-size: 14px;
}

/* Content Sections */
.content-section {
    transition: opacity 0.3s ease-in-out;
    opacity: 1;
}

.content-section:not(.active) {
    display: none !important;
    opacity: 0;
}

.content-section.active,
.feed.content-section.active {
    display: flex !important;
    flex-direction: column;
    opacity: 1;
    min-height: 400px;
}

/* Mobile Navigation */
.mobile-nav-item {
    transition: all 0.3s ease;
}

.mobile-nav-item.touch-active {
    background: rgba(139, 69, 19, 0.2);
    transform: scale(0.95);
}

/* Loading States */
.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #f0f0f0;
    border-top: 3px solid #8B4513;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.hidden { display: none !important; }
.visible { display: block !important; }

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.sidebar-item:focus,
.nav-btn:focus,
.refresh-btn:focus {
    outline: 2px solid #8B4513;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .sidebar-item:hover {
        background: rgba(139, 69, 19, 0.3);
    }
    
    .nav-btn {
        border-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* Modern Modal System */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 1000;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: #ffffff;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    width: 90%;
    max-width: 500px;
    position: relative;
    transform: scale(0.95);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal-content {
    transform: scale(1);
}

.modal-close-button {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--color-text);
}
