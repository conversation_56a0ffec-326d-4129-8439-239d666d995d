<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share</title>

    <!-- External CSS Files -->
    <link rel="stylesheet" href="./public/css/variables.css">
    <link rel="stylesheet" href="./public/css/main.css">
    <link rel="stylesheet" href="./public/css/preferences.css">
    <link rel="stylesheet" href="./public/css/responsive.css">
    <link rel="stylesheet" href="./public/css/posts.css">
    <link rel="stylesheet" href="./public/css/forms.css">
    <link rel="stylesheet" href="./public/css/notifications.css">
    <link rel="stylesheet" href="./public/css/performance.css">
    <link rel="stylesheet" href="./public/css/mobile-performance.css">
    <link rel="stylesheet" href="./public/css/scroll-sections.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family-primary);
            background: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: var(--breakpoint-2xl);
            margin: 0 auto;
            padding: 0 var(--spacing-lg);
        }

        /* Header */
        .header {
            background: var(--header-bg);
            backdrop-filter: var(--header-backdrop-blur);
            border-bottom: var(--card-border);
            position: sticky;
            top: 0;
            z-index: var(--z-sticky);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md) 0;
        }

        .logo {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-extrabold);
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: var(--shadow-glow);
        }

        .search-container {
            position: relative;
            flex: 1;
            max-width: 400px;
            margin: 0 var(--spacing-2xl);
        }

        .search-input {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md) 50px;
            background: var(--bg-input);
            border: var(--input-border);
            border-radius: var(--radius-2xl);
            color: var(--text-primary);
            font-size: var(--font-size-base);
            transition: var(--transition-normal);
        }

        .search-input:focus {
            outline: none;
            background: var(--bg-input-focus);
            border-color: var(--border-accent);
            box-shadow: var(--shadow-glow);
        }

        .search-input::placeholder {
            color: var(--text-subtle); /* Improved contrast for WCAG AA */
        }

        .search-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.75); /* Improved contrast for WCAG AA */
        }

        .user-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
        }

        .notification-btn, .preferences-btn {
            position: relative;
            padding: var(--spacing-md);
            background: var(--bg-input);
            border: var(--input-border);
            border-radius: var(--radius-full);
            color: var(--text-primary);
            cursor: pointer;
            transition: var(--transition-normal);
            font-size: var(--font-size-lg);
        }

        .notification-btn:hover, .preferences-btn:hover {
            background: var(--bg-card-hover);
            border-color: var(--border-accent);
            transform: scale(1.05);
        }

        .profile-img {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background: var(--gradient-button);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: var(--font-weight-bold);
            cursor: pointer;
            transition: var(--transition-normal);
            border: 2px solid var(--border-accent);
            box-shadow: var(--shadow-glow);
        }

        .profile-img:hover {
            transform: scale(1.1);
        }

        /* Main Content */
        .main-content {
            display: grid;
            grid-template-columns: var(--sidebar-width) 1fr 320px;
            gap: var(--spacing-2xl);
            padding: var(--spacing-2xl) 0;
            min-height: calc(100vh - var(--header-height));
        }

        /* Sidebar */
        .sidebar {
            background: var(--bg-card);
            border-radius: var(--radius-xl);
            padding: var(--card-padding);
            height: fit-content;
            position: sticky;
            top: 110px;
            border: var(--card-border);
            backdrop-filter: var(--card-backdrop-blur);
        }

        .sidebar h3 {
            color: var(--color-primary);
            margin-bottom: var(--spacing-lg);
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            padding: var(--spacing-lg) var(--spacing-xl);
            margin-bottom: var(--spacing-sm);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: var(--transition-normal);
            border: 1px solid transparent;
        }

        .nav-item:hover {
            background: var(--bg-card-hover);
            border-color: var(--border-focus);
            transform: translateX(var(--spacing-xs));
        }

        .nav-item.active {
            background: var(--gradient-card);
            border-color: var(--border-accent);
        }

        .nav-icon {
            font-size: 20px;
            color: #ff6b35;
        }

        /* Content Sections */
        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* Feed Section */
        .feed {
            background: var(--bg-card);
            border-radius: var(--radius-xl);
            padding: 0;
            border: var(--card-border);
            backdrop-filter: var(--card-backdrop-blur);
            box-shadow: var(--shadow-md);
            transition: var(--transition-normal);
        }

        .feed-header {
            padding: var(--card-padding);
            border-bottom: var(--card-border);
        }

        .feed-title {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            margin-bottom: var(--spacing-lg);
            background: linear-gradient(45deg, #ffffff, var(--color-primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .story-prompt {
            background: var(--gradient-card);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            border: 1px solid rgba(255, 107, 53, 0.3);
            margin-bottom: var(--spacing-lg);
        }

        .story-prompt h4 {
            color: var(--color-primary);
            margin-bottom: var(--spacing-sm);
            font-size: var(--font-size-lg);
        }

        .story-prompt p {
            color: var(--text-muted);
            margin-bottom: var(--spacing-lg);
            line-height: var(--line-height-relaxed);
        }

        .create-post-btn {
            background: var(--gradient-button);
            color: var(--text-primary);
            border: none;
            padding: var(--spacing-lg) var(--spacing-2xl);
            border-radius: var(--radius-2xl);
            font-weight: var(--font-weight-semibold);
            cursor: pointer;
            transition: var(--transition-normal);
            font-size: var(--font-size-base);
            width: 100%;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-glow);
        }

        .create-post-btn:hover {
            background: var(--gradient-button-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-glow-strong);
        }

        .create-post-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .create-post-btn:hover::before {
            left: 100%;
        }

        .feed-content {
            padding: var(--card-padding);
        }

        .load-more {
            text-align: center;
            padding: var(--spacing-lg);
            color: var(--text-muted); /* Improved contrast for WCAG AA */
            cursor: pointer;
            transition: var(--transition-normal);
            border-radius: var(--radius-lg);
            margin: var(--spacing-lg);
            background: var(--bg-input);
            border: var(--input-border);
        }

        .load-more:hover {
            color: var(--color-primary);
            background: var(--bg-card-hover);
            border-color: var(--border-accent);
        }

        /* Empty State Styling */
        .empty-state {
            text-align: center;
            padding: var(--spacing-3xl);
            background: var(--gradient-card);
            border-radius: var(--radius-xl);
            border: 1px solid rgba(255, 107, 53, 0.2);
            margin: var(--spacing-lg);
        }

        .empty-state h4 {
            color: var(--color-primary);
            font-size: var(--font-size-xl);
            margin-bottom: var(--spacing-md);
            font-weight: var(--font-weight-semibold);
        }

        .empty-state p {
            color: var(--text-muted);
            line-height: var(--line-height-relaxed);
            max-width: 400px;
            margin: 0 auto;
        }

        /* Loading State Styling */
        .loading-state {
            padding: var(--spacing-lg);
        }

        /* Trending Sidebar */
        .trending {
            background: var(--bg-card);
            border-radius: var(--radius-xl);
            padding: var(--card-padding);
            height: fit-content;
            position: sticky;
            top: 110px;
            border: var(--card-border);
            backdrop-filter: var(--card-backdrop-blur);
            box-shadow: var(--shadow-md);
            transition: var(--transition-normal);
        }

        .trending h3 {
            color: var(--color-primary);
            margin-bottom: var(--spacing-xl);
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
        }

        .trending-item {
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-lg);
            background: var(--bg-card);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: var(--transition-normal);
            border: 1px solid transparent;
        }

        .trending-item:hover {
            background: var(--bg-card-hover);
            border-color: var(--border-focus);
            transform: translateY(-2px);
        }

        .trending-hashtag {
            color: var(--color-primary);
            font-weight: var(--font-weight-semibold);
            margin-bottom: var(--spacing-xs);
        }

        .trending-count {
            color: var(--text-muted); /* Improved contrast for WCAG AA */
            font-size: var(--font-size-sm);
        }

        /* Trending Topic Detail Page */
        .trending-topic-detail {
            max-width: 100%;
        }

        .topic-header {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .topic-header h3 {
            color: #ff6b35;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .topic-header p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            margin-bottom: 0;
        }

        .topic-description {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .topic-description p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            line-height: 1.6;
            margin: 0;
        }

        .topic-posts {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }



        /* Section Content Styles */
        .explore-content, .messages-content, .profile-content, .communities-content, .analytics-content {
            padding: 20px;
            text-align: center;
        }

        .explore-content h3, .messages-content h3, .profile-content h3, .communities-content h3, .analytics-content h3 {
            color: #ff6b35;
            margin-bottom: 15px;
            font-size: 24px;
        }

        .explore-content p, .messages-content p, .communities-content p, .analytics-content p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .explore-categories {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .category-tag {
            background: rgba(255, 107, 53, 0.2);
            color: #ff6b35;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 107, 53, 0.3);
        }

        .category-tag:hover {
            background: rgba(255, 107, 53, 0.3);
            transform: translateY(-2px);
        }

        /* Profile Styles */
        .profile-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            justify-content: center;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: bold;
        }

        .profile-info h3 {
            color: #ffffff;
            margin-bottom: 5px;
            font-size: 24px;
        }

        .profile-info p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
        }

        .profile-stats {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-bottom: 30px;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 28px;
            font-weight: bold;
            color: #ff6b35;
            margin-bottom: 5px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        .nav-btn {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.4);
        }

        /* Communities Styles */
        .communities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .community-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .community-card:hover {
            background: rgba(255, 107, 53, 0.1);
            border-color: rgba(255, 107, 53, 0.3);
            transform: translateY(-5px);
        }

        .community-card h4 {
            color: #ff6b35;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .community-card p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
            font-size: 14px;
        }

        .join-btn {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .join-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
        }

        /* Analytics Styles */
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .analytics-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .analytics-card:hover {
            background: rgba(255, 107, 53, 0.1);
            border-color: rgba(255, 107, 53, 0.3);
            transform: translateY(-5px);
        }

        .analytics-card h4 {
            color: #ff6b35;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .analytics-number {
            font-size: 36px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 10px;
        }

        .analytics-card p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            margin: 0;
        }

        /* Messages Styles */
        .messages-placeholder {
            background: var(--bg-card);
            border-radius: var(--radius-lg);
            padding: var(--spacing-3xl);
            border: var(--card-border);
            margin-top: var(--spacing-lg);
            text-align: center;
        }

        .messages-placeholder p {
            color: var(--text-muted);
            font-style: italic;
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .sidebar, .feed, .trending {
            animation: fadeIn 0.6s ease-out;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 280px 1fr 300px;
                gap: var(--spacing-lg);
            }
        }

        @media (max-width: 960px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: var(--spacing-lg);
                padding: var(--spacing-lg) 0;
            }

            .sidebar, .trending {
                position: static;
                margin-bottom: var(--spacing-lg);
            }

            .search-container {
                margin: 0 var(--spacing-lg);
                max-width: none;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 var(--spacing-md);
            }

            .header-content {
                padding: var(--spacing-sm) 0;
            }

            .logo {
                font-size: var(--font-size-2xl);
            }

            .user-actions {
                gap: var(--spacing-md);
            }

            .notification-btn, .preferences-btn {
                padding: var(--spacing-sm);
                font-size: var(--font-size-base);
            }

            .profile-img {
                width: 35px;
                height: 35px;
            }

            .main-content {
                padding: var(--spacing-md) 0;
                gap: var(--spacing-md);
            }

            .feed-header, .feed-content {
                padding: var(--spacing-lg);
            }

            .story-prompt {
                padding: var(--spacing-lg);
            }

            .create-post-btn {
                padding: var(--spacing-md) var(--spacing-lg);
                font-size: var(--font-size-sm);
            }
            .header-content {
                flex-direction: column;
                gap: 20px;
            }

            .search-container {
                max-width: 100%;
                margin: 0;
            }

            .container {
                padding: 0 15px;
            }
        }

        /* Screen Reader Only */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
    </style>
</head>
<body>
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">Naroop</div>
                <div class="search-container">
                    <div class="search-icon" aria-hidden="true">🔍</div>
                    <label for="search-input" class="sr-only">Search communities, topics, and people</label>
                    <input type="text" id="search-input" class="search-input"
                           placeholder="Search communities, topics, people..."
                           aria-label="Search communities, topics, and people">
                </div>
                <div class="user-actions">
                    <button class="preferences-btn" id="preferencesBtn" aria-label="Open accessibility preferences">
                        <span aria-hidden="true">⚙️</span>
                    </button>
                    <button class="notification-btn" aria-label="View notifications">
                        <span aria-hidden="true">🔔</span>
                    </button>
                    <div class="profile-img" role="button" tabindex="0" aria-label="User profile menu">N</div>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="main-content" id="main-content">
            <aside class="sidebar card-base card-padding card-sticky">
                <h3>Navigation</h3>
                <nav role="navigation" aria-label="Main navigation">
                    <div class="nav-item active" data-section="feed" aria-current="page" role="button" tabindex="0" aria-label="Feed - Current page">
                        <div class="nav-icon" aria-hidden="true">📱</div>
                        <span>Feed</span>
                    </div>
                    <div class="nav-item" data-section="explore" role="button" tabindex="0" aria-label="Explore content">
                        <div class="nav-icon" aria-hidden="true">🌟</div>
                        <span>Explore</span>
                    </div>
                    <div class="nav-item" data-section="messages" role="button" tabindex="0" aria-label="Messages">
                        <div class="nav-icon" aria-hidden="true">💬</div>
                        <span>Messages</span>
                    </div>
                    <div class="nav-item" data-section="profile" role="button" tabindex="0" aria-label="Profile">
                        <div class="nav-icon" aria-hidden="true">👤</div>
                        <span>Profile</span>
                    </div>
                    <div class="nav-item" data-section="communities" role="button" tabindex="0" aria-label="Communities">
                        <div class="nav-icon" aria-hidden="true">🎯</div>
                        <span>Communities</span>
                    </div>
                    <div class="nav-item" data-section="analytics" role="button" tabindex="0" aria-label="Analytics">
                        <div class="nav-icon" aria-hidden="true">📊</div>
                        <span>Analytics</span>
                    </div>
                </nav>
            </aside>

            <!-- Feed Section -->
            <main class="feed content-section active" id="feed-section">
                <div class="feed-header">
                    <h2 class="feed-title">Your Feed</h2>
                    <div class="story-prompt">
                        <h4><span aria-hidden="true">✨</span> Share Your Story</h4>
                        <p>What positive experience would you like to share with the community today?</p>
                        <button class="create-post-btn btn-base btn-primary btn-lg" id="createPostBtn" onclick="addBounceEffect(this)">Create Post</button>
                    </div>
                </div>
                <div class="feed-content">
                    <!-- Loading State (hidden by default) -->
                    <div class="loading-state" id="feedLoading" style="display: none;">
                        <div class="loading-skeleton card"></div>
                        <div class="loading-skeleton card"></div>
                        <div class="loading-skeleton card"></div>
                    </div>

                    <!-- Empty State -->
                    <div class="empty-state" id="feedEmpty">
                        <h4>Welcome to your feed!</h4>
                        <p>Start by creating your first post or connecting with other community members. Your positive stories and experiences will appear here.</p>
                    </div>

                    <!-- Posts Container -->
                    <div id="postsContainer">
                        <!-- Posts will be loaded here -->
                    </div>

                    <div class="load-more">Load More Posts</div>
                </div>
            </main>

            <!-- Explore Section -->
            <section class="feed content-section" id="explore-section">
                <div class="feed-header">
                    <h2 class="feed-title">Explore</h2>
                </div>
                <div class="feed-content">
                    <div class="explore-content">
                        <h3><span aria-hidden="true">🔍</span> Discover New Content</h3>
                        <p>Explore trending posts and discover new voices in the community.</p>
                        <div class="explore-categories">
                            <div class="category-tag">#Trending</div>
                            <div class="category-tag">#BlackExcellence</div>
                            <div class="category-tag">#CommunityLove</div>
                            <div class="category-tag">#Inspiration</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Trending Topics Section -->
            <section class="feed content-section" id="trending-section">
                <div class="feed-header">
                    <h2 class="feed-title">Trending Topics</h2>
                </div>
                <div class="feed-content">
                    <div class="trending-topic-detail" id="trending-detail-container">
                        <div class="empty-state">
                            <div class="empty-state-icon" aria-hidden="true">📈</div>
                            <h3>No trending topics yet</h3>
                            <p>Trending topics will appear here as the community grows and creates engaging content.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Messages Section -->
            <section class="feed content-section" id="messages-section">
                <div class="feed-header">
                    <h2 class="feed-title">Messages</h2>
                </div>
                <div class="feed-content">
                    <div class="messages-content">
                        <h3><span aria-hidden="true">💬</span> Your Messages</h3>
                        <p>Connect and communicate with your community.</p>
                        <div class="messages-placeholder">
                            <p>No messages yet. Start a conversation!</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section class="feed content-section" id="profile-section">
                <div class="feed-header">
                    <h2 class="feed-title">Profile</h2>
                </div>
                <div class="feed-content">
                    <div class="profile-content">
                        <div class="profile-header">
                            <div class="profile-avatar">👤</div>
                            <div class="profile-info">
                                <h3 id="profileUsername">Loading...</h3>
                                <p id="profileEmail">Loading...</p>
                            </div>
                        </div>
                        <div class="profile-stats">
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Posts</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Followers</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Following</span>
                            </div>
                        </div>
                        <div class="profile-actions">
                            <button class="nav-btn" id="signOutBtn">Sign Out</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Communities Section -->
            <section class="feed content-section" id="communities-section">
                <div class="feed-header">
                    <h2 class="feed-title">Communities</h2>
                </div>
                <div class="feed-content">
                    <div class="communities-content">
                        <h3><span aria-hidden="true">🎯</span> Join Communities</h3>
                        <p>Connect with like-minded people and join communities that inspire you.</p>
                        <div class="communities-grid" id="communities-container">
                            <div class="empty-state">
                                <div class="empty-state-icon" aria-hidden="true">👥</div>
                                <h4>No communities yet</h4>
                                <p>Communities will be created as the platform grows. Be the first to start meaningful conversations!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Analytics Section -->
            <section class="feed content-section" id="analytics-section">
                <div class="feed-header">
                    <h2 class="feed-title">Analytics</h2>
                </div>
                <div class="feed-content">
                    <div class="analytics-content">
                        <h3><span aria-hidden="true">📊</span> Your Analytics</h3>
                        <p>Track your engagement and community impact.</p>
                        <div class="analytics-grid" id="analytics-container">
                            <div class="empty-state">
                                <div class="empty-state-icon" aria-hidden="true">📊</div>
                                <h4>No analytics data yet</h4>
                                <p>Start posting and engaging with the community to see your analytics here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <aside class="trending card-base card-padding card-sticky">
                <h3>Trending Topics</h3>
                <div id="trending-container">
                    <div class="empty-state">
                        <div class="empty-state-icon" aria-hidden="true">📈</div>
                        <h4>No trending topics yet</h4>
                        <p>Trending topics will appear here as the community grows and engages with content.</p>
                    </div>
                </div>
            </aside>
        </div>
    </div>
    <!-- Accessibility Preferences Panel -->
    <div class="preferences-panel" id="preferencesPanel" role="dialog" aria-labelledby="preferences-title" aria-hidden="true">
        <h3 id="preferences-title">Accessibility Preferences</h3>

        <div class="preference-group">
            <h4>Visual Preferences</h4>
            <div class="toggle-switch">
                <label for="high-contrast">High Contrast Mode</label>
                <input type="checkbox" id="high-contrast" aria-describedby="high-contrast-desc">
            </div>
            <p id="high-contrast-desc" class="preference-description">Increases contrast for better visibility</p>

            <div class="toggle-switch">
                <label for="large-text">Large Text</label>
                <input type="checkbox" id="large-text" aria-describedby="large-text-desc">
            </div>
            <p id="large-text-desc" class="preference-description">Increases font size for better readability</p>
        </div>

        <div class="preference-group">
            <h4>Motion Preferences</h4>
            <div class="toggle-switch">
                <label for="reduce-motion">Reduce Motion</label>
                <input type="checkbox" id="reduce-motion" aria-describedby="reduce-motion-desc">
            </div>
            <p id="reduce-motion-desc" class="preference-description">Minimizes animations and transitions</p>
        </div>

        <div class="preference-group">
            <h4>Keyboard Navigation</h4>
            <div class="toggle-switch">
                <label for="enhanced-focus">Enhanced Focus Indicators</label>
                <input type="checkbox" id="enhanced-focus" aria-describedby="enhanced-focus-desc">
            </div>
            <p id="enhanced-focus-desc" class="preference-description">Makes focus indicators more visible</p>
        </div>

        <button class="btn-base btn-primary btn-md" onclick="closePreferences()" style="width: 100%; margin-top: var(--spacing-lg);">
            Close Preferences
        </button>
    </div>











    <!-- Include essential JavaScript files for functionality -->
    <script type="module" src="./public/js/firebase-config.js"></script>
    <script type="module" src="./public/js/authentication.js"></script>
    <script type="module" src="./public/js/core.js"></script>
    <script type="module" src="./public/js/navigation.js"></script>
    <script type="module" src="./public/js/posts.js"></script>
    <script type="module" src="./public/js/profile.js"></script>
    <script src="./public/js/modal-system.js"></script>
    <script src="./public/js/accessibility-preferences.js"></script>

    <script>
    // Notification button handler
    document.addEventListener('DOMContentLoaded', function() {
      var notificationBtn = document.querySelector('.notification-btn');
      if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
          if (window.ModalSystem && window.ModalSystem.open) {
            window.ModalSystem.open('<h2>Notifications</h2><p>No new notifications yet.</p>');
          } else if (window.ErrorHandler) {
            window.ErrorHandler.showNotification('No new notifications yet.', 'info');
          } else {
            console.log('No new notifications yet.');
          }
        });
      }

      // Note: Community and post interaction handlers are now managed by the Posts module
    });

    // Micro-interactions and UI enhancements
    function addBounceEffect(element) {
        if (!document.body.classList.contains('reduce-motion-mode')) {
            element.classList.add('bounce-on-click');
            setTimeout(() => {
                element.classList.remove('bounce-on-click');
            }, 600);
        }
    }

    // Show loading state
    function showLoadingState() {
        const loading = document.getElementById('feedLoading');
        const empty = document.getElementById('feedEmpty');
        const container = document.getElementById('postsContainer');

        if (loading) loading.style.display = 'block';
        if (empty) empty.style.display = 'none';
        if (container) container.style.display = 'none';
    }

    // Hide loading state
    function hideLoadingState() {
        const loading = document.getElementById('feedLoading');
        if (loading) loading.style.display = 'none';
    }

    // Show empty state
    function showEmptyState() {
        const empty = document.getElementById('feedEmpty');
        const container = document.getElementById('postsContainer');

        if (empty) empty.style.display = 'block';
        if (container) container.style.display = 'none';
    }

    // Show posts container
    function showPostsContainer() {
        const empty = document.getElementById('feedEmpty');
        const container = document.getElementById('postsContainer');

        if (empty) empty.style.display = 'none';
        if (container) {
            container.style.display = 'block';
            container.classList.add('fade-in-up');
        }
    }

    // Add notification pulse effect
    function addNotificationPulse() {
        const notificationBtn = document.querySelector('.notification-btn');
        if (notificationBtn && !document.body.classList.contains('reduce-motion-mode')) {
            notificationBtn.classList.add('notification-pulse');
            setTimeout(() => {
                notificationBtn.classList.remove('notification-pulse');
            }, 2000);
        }
    }

    // Enhanced keyboard navigation
    document.addEventListener('keydown', function(e) {
        // Handle Enter key on elements with tabindex
        if (e.key === 'Enter') {
            const target = e.target;
            if (target.hasAttribute('tabindex') && target.getAttribute('role') === 'button') {
                target.click();
            }
        }

        // Handle arrow key navigation in sidebar
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
            const navItems = document.querySelectorAll('.nav-item[tabindex="0"]');
            const currentIndex = Array.from(navItems).indexOf(document.activeElement);

            if (currentIndex !== -1) {
                e.preventDefault();
                let nextIndex;

                if (e.key === 'ArrowDown') {
                    nextIndex = (currentIndex + 1) % navItems.length;
                } else {
                    nextIndex = (currentIndex - 1 + navItems.length) % navItems.length;
                }

                navItems[nextIndex].focus();
            }
        }
    });

    // Initialize UI enhancements
    document.addEventListener('DOMContentLoaded', function() {
        // Add fade-in animation to main content
        const mainContent = document.querySelector('.main-content');
        if (mainContent && !document.body.classList.contains('reduce-motion-mode')) {
            mainContent.classList.add('fade-in-up');
        }

        // Simulate loading state for demonstration
        setTimeout(() => {
            hideLoadingState();
            showEmptyState();
        }, 1000);
    });
    </script>

    <!-- Generic Modal Structure -->
    <div id="action-modal" class="modal-overlay" aria-hidden="true">
        <div class="modal-content" role="dialog" aria-modal="true">
            <div id="modal-body-content">
                <!-- Dynamic content will be loaded here by JavaScript -->
            </div>
            <button class="modal-close-button" aria-label="Close modal">&times;</button>
        </div>
    </div>
</body>
</html>
