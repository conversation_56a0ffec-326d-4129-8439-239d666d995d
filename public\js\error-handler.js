// Comprehensive Error Handling and User Feedback System for Naroop

export class ErrorHandler {
    constructor() {
        this.errorQueue = [];
        this.isInitialized = false;
        this.retryAttempts = new Map();
        this.maxRetries = 3;
        this.retryDelay = 1000; // 1 second base delay
    }

    // Initialize error handling system
    init() {
        if (this.isInitialized) {
            console.warn('ErrorHandler already initialized');
            return;
        }

        this.setupGlobalErrorHandlers();
        this.createNotificationContainer();
        this.isInitialized = true;
        console.log('🛡️ Error handling system initialized');
    }

    // Setup global error handlers
    setupGlobalErrorHandlers() {
        // Handle unhandled JavaScript errors
        window.addEventListener('error', (event) => {
            this.handleGlobalError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.handleGlobalError({
                type: 'promise',
                message: event.reason?.message || 'Unhandled promise rejection',
                reason: event.reason
            });
            event.preventDefault(); // Prevent console error
        });

        // Handle network errors
        window.addEventListener('offline', () => {
            this.showNotification('You are currently offline. Some features may not work.', 'warning', 0);
        });

        window.addEventListener('online', () => {
            this.showNotification('Connection restored!', 'success', 3000);
            this.retryFailedRequests();
        });
    }

    // Handle global errors
    handleGlobalError(errorInfo) {
        console.error('Global error caught:', errorInfo);
        
        // Don't show notifications for every JS error in production
        if (this.isDevelopment()) {
            this.showNotification(
                `Error: ${errorInfo.message}`,
                'error',
                5000
            );
        } else {
            // In production, log to monitoring service
            this.logToMonitoring(errorInfo);
        }
    }

    // Create notification container
    createNotificationContainer() {
        if (document.getElementById('notification-container')) {
            return; // Already exists
        }

        const container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // Show notification to user
    showNotification(message, type = 'info', duration = 4000, actions = null) {
        const notification = this.createNotificationElement(message, type, actions);
        const container = document.getElementById('notification-container');
        
        if (!container) {
            console.error('Notification container not found');
            return null;
        }

        container.appendChild(notification);

        // Trigger animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Auto-remove notification
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
        }

        return notification;
    }

    // Create notification element
    createNotificationElement(message, type, actions) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const icon = this.getNotificationIcon(type);
        const timestamp = new Date().toLocaleTimeString();
        
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-header">
                    <span class="notification-icon">${icon}</span>
                    <span class="notification-time">${timestamp}</span>
                    <button class="notification-close" onclick="this.closest('.notification').remove()">×</button>
                </div>
                <div class="notification-message">${message}</div>
                ${actions ? `<div class="notification-actions">${actions}</div>` : ''}
            </div>
        `;

        // Add click to dismiss
        notification.addEventListener('click', (e) => {
            if (e.target.classList.contains('notification-close')) {
                this.removeNotification(notification);
            }
        });

        return notification;
    }

    // Get icon for notification type
    getNotificationIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️',
            loading: '⏳'
        };
        return icons[type] || icons.info;
    }

    // Remove notification
    removeNotification(notification) {
        if (!notification || !notification.parentNode) return;
        
        notification.classList.add('hide');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    // Handle API errors with retry logic
    async handleApiError(error, requestInfo, retryCallback = null) {
        const errorKey = `${requestInfo.method}-${requestInfo.url}`;
        const attempts = this.retryAttempts.get(errorKey) || 0;

        console.error('API Error:', error, requestInfo);

        // Determine error type and user message
        let userMessage = 'An unexpected error occurred';
        let shouldRetry = false;
        let retryable = false;

        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            userMessage = 'Network connection error. Please check your internet connection.';
            retryable = true;
        } else if (error.status) {
            switch (error.status) {
                case 400:
                    userMessage = 'Invalid request. Please check your input.';
                    break;
                case 401:
                    userMessage = 'Authentication required. Please sign in again.';
                    this.handleAuthenticationError();
                    break;
                case 403:
                    userMessage = 'Access denied. You don\'t have permission for this action.';
                    break;
                case 404:
                    userMessage = 'The requested resource was not found.';
                    break;
                case 429:
                    userMessage = 'Too many requests. Please wait a moment and try again.';
                    retryable = true;
                    break;
                case 500:
                    userMessage = 'Server error. Our team has been notified.';
                    retryable = true;
                    break;
                case 503:
                    userMessage = 'Service temporarily unavailable. Please try again later.';
                    retryable = true;
                    break;
                default:
                    userMessage = `Error ${error.status}: ${error.message || 'Unknown error'}`;
                    retryable = error.status >= 500;
            }
        }

        // Show error notification
        let actions = '';
        if (retryable && attempts < this.maxRetries && retryCallback) {
            shouldRetry = true;
            actions = `<button class="retry-btn" onclick="window.ErrorHandler.retryRequest('${errorKey}')">Retry</button>`;
        }

        const notification = this.showNotification(userMessage, 'error', shouldRetry ? 0 : 5000, actions);

        // Store retry information
        if (shouldRetry && retryCallback) {
            this.retryAttempts.set(errorKey, attempts + 1);
            this.errorQueue.push({
                key: errorKey,
                callback: retryCallback,
                notification: notification,
                attempts: attempts + 1
            });
        }

        return {
            handled: true,
            shouldRetry: shouldRetry,
            userMessage: userMessage
        };
    }

    // Retry a specific request
    async retryRequest(errorKey) {
        const errorInfo = this.errorQueue.find(item => item.key === errorKey);
        if (!errorInfo) return;

        // Remove from queue
        this.errorQueue = this.errorQueue.filter(item => item.key !== errorKey);
        
        // Remove notification
        if (errorInfo.notification) {
            this.removeNotification(errorInfo.notification);
        }

        // Show retry notification
        this.showNotification('Retrying...', 'loading', 2000);

        // Wait before retry (exponential backoff)
        const delay = this.retryDelay * Math.pow(2, errorInfo.attempts - 1);
        await new Promise(resolve => setTimeout(resolve, delay));

        try {
            await errorInfo.callback();
            this.showNotification('Request successful!', 'success', 3000);
            this.retryAttempts.delete(errorKey);
        } catch (error) {
            // Handle retry failure
            await this.handleApiError(error, { method: 'RETRY', url: errorKey }, errorInfo.callback);
        }
    }

    // Retry all failed requests (when coming back online)
    async retryFailedRequests() {
        const failedRequests = [...this.errorQueue];
        this.errorQueue = [];

        for (const request of failedRequests) {
            try {
                await request.callback();
                this.retryAttempts.delete(request.key);
                if (request.notification) {
                    this.removeNotification(request.notification);
                }
            } catch (error) {
                // Re-add to queue if still failing
                this.errorQueue.push(request);
            }
        }

        if (failedRequests.length > 0) {
            this.showNotification(
                `Retried ${failedRequests.length - this.errorQueue.length} of ${failedRequests.length} failed requests`,
                'info',
                3000
            );
        }
    }

    // Handle authentication errors
    handleAuthenticationError() {
        // Clear any stored authentication data
        if (window.secureAuth) {
            window.secureAuth.signOut();
        }
        
        // Redirect to login or show login modal
        setTimeout(() => {
            if (window.location.pathname !== '/landing.html') {
                window.location.href = '/landing.html';
            }
        }, 2000);
    }

    // Validate form with real-time feedback
    validateForm(formElement, validationRules) {
        const errors = {};
        let isValid = true;

        for (const [fieldName, rules] of Object.entries(validationRules)) {
            const field = formElement.querySelector(`[name="${fieldName}"]`);
            if (!field) continue;

            const value = field.value.trim();
            const fieldErrors = [];

            // Check each validation rule
            for (const rule of rules) {
                if (rule.required && !value) {
                    fieldErrors.push(rule.message || `${fieldName} is required`);
                } else if (value && rule.pattern && !rule.pattern.test(value)) {
                    fieldErrors.push(rule.message || `${fieldName} format is invalid`);
                } else if (value && rule.minLength && value.length < rule.minLength) {
                    fieldErrors.push(rule.message || `${fieldName} must be at least ${rule.minLength} characters`);
                } else if (value && rule.maxLength && value.length > rule.maxLength) {
                    fieldErrors.push(rule.message || `${fieldName} must be no more than ${rule.maxLength} characters`);
                } else if (rule.custom && !rule.custom(value)) {
                    fieldErrors.push(rule.message || `${fieldName} is invalid`);
                }
            }

            if (fieldErrors.length > 0) {
                errors[fieldName] = fieldErrors;
                isValid = false;
                this.showFieldError(field, fieldErrors[0]);
            } else {
                this.clearFieldError(field);
            }
        }

        return { isValid, errors };
    }

    // Show field-specific error
    showFieldError(field, message) {
        field.classList.add('error');
        
        let errorElement = field.parentNode.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            field.parentNode.appendChild(errorElement);
        }
        
        errorElement.textContent = message;
        errorElement.classList.add('show');
    }

    // Clear field error
    clearFieldError(field) {
        field.classList.remove('error');
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.classList.remove('show');
        }
    }

    // Check if in development mode
    isDevelopment() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.hostname.includes('dev');
    }

    // Log to monitoring service (placeholder)
    logToMonitoring(errorInfo) {
        // In a real application, this would send to a monitoring service
        console.log('Would log to monitoring service:', errorInfo);
    }

    // Show loading state
    showLoading(element, message = 'Loading...') {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (!element) return null;

        element.classList.add('loading');
        element.disabled = true;
        
        const originalContent = element.innerHTML;
        element.innerHTML = `<span class="loading-spinner"></span> ${message}`;
        
        return () => {
            element.classList.remove('loading');
            element.disabled = false;
            element.innerHTML = originalContent;
        };
    }

    // Hide loading state
    hideLoading(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (!element) return;

        element.classList.remove('loading');
        element.disabled = false;
    }
}

// Create global instance
export const errorHandler = new ErrorHandler();

// Make available globally for backward compatibility
window.ErrorHandler = errorHandler;
