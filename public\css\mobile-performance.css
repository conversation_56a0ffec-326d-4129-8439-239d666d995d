/* Mobile Performance Optimization CSS
   Specific optimizations for mobile devices and touch interactions */

/* Mobile-first performance optimizations */
@media (max-width: 768px) {
    /* Reduce complexity for mobile */
    * {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
    }
    
    /* Optimize touch interactions */
    .touch-target {
        min-height: 44px;
        min-width: 44px;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }
    
    /* Optimize scrolling performance */
    .scroll-container {
        -webkit-overflow-scrolling: touch;
        overflow-scrolling: touch;
        scroll-behavior: smooth;
        will-change: scroll-position;
    }
    
    /* Reduce animations on mobile */
    .mobile-optimized {
        animation-duration: 0.2s !important;
        transition-duration: 0.2s !important;
    }
    
    /* Optimize images for mobile */
    img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: optimize-contrast;
        -ms-interpolation-mode: nearest-neighbor;
    }
    
    /* Optimize fonts for mobile */
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeSpeed;
    }
    
    /* Reduce paint complexity */
    .mobile-card {
        contain: layout style paint;
        will-change: transform;
    }
    
    /* Optimize form inputs for mobile */
    input, textarea, select {
        -webkit-appearance: none;
        appearance: none;
        border-radius: 0;
        touch-action: manipulation;
    }
    
    /* Optimize buttons for mobile */
    button, .btn {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
        user-select: none;
        -webkit-user-select: none;
    }
    
    /* Optimize modal performance on mobile */
    .modal {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        will-change: transform, opacity;
    }
    
    /* Reduce motion for better performance */
    .reduce-motion {
        animation: none !important;
        transition: none !important;
    }
    
    /* Optimize list performance */
    .mobile-list {
        contain: layout style;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .mobile-list-item {
        contain: layout style;
        will-change: transform;
    }
    
    /* Optimize navigation for mobile */
    .mobile-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #ddd;
        padding: 10px;
        display: flex;
        justify-content: space-around;
        z-index: 1000;
        contain: layout style paint;
    }
    
    .mobile-nav-item {
        flex: 1;
        text-align: center;
        padding: 10px;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }
    
    /* Optimize header for mobile */
    .mobile-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: #8B4513;
        color: white;
        padding: 15px;
        z-index: 1000;
        contain: layout style paint;
    }
    
    /* Add safe area support for notched devices */
    .mobile-header {
        padding-top: max(15px, env(safe-area-inset-top));
    }
    
    .mobile-nav {
        padding-bottom: max(10px, env(safe-area-inset-bottom));
    }
    
    /* Optimize content area for mobile */
    .mobile-content {
        margin-top: 70px; /* Account for fixed header */
        margin-bottom: 80px; /* Account for fixed navigation */
        padding: 15px;
        contain: layout style;
    }
    
    /* Optimize post cards for mobile */
    .mobile-post-card {
        margin-bottom: 15px;
        padding: 15px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        contain: layout style paint;
        will-change: transform;
    }
    
    /* Optimize images in posts for mobile */
    .mobile-post-image {
        width: 100%;
        height: auto;
        border-radius: 4px;
        object-fit: cover;
        loading: lazy;
        decoding: async;
    }
    
    /* Optimize text for mobile reading */
    .mobile-text {
        line-height: 1.6;
        font-size: 16px; /* Prevent zoom on iOS */
        -webkit-text-size-adjust: 100%;
        text-size-adjust: 100%;
    }
    
    /* Optimize forms for mobile */
    .mobile-form {
        padding: 20px;
    }
    
    .mobile-form input,
    .mobile-form textarea {
        width: 100%;
        padding: 15px;
        font-size: 16px; /* Prevent zoom on iOS */
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 15px;
        box-sizing: border-box;
    }
    
    .mobile-form button {
        width: 100%;
        padding: 15px;
        font-size: 16px;
        background: #8B4513;
        color: white;
        border: none;
        border-radius: 4px;
        touch-action: manipulation;
    }
    
    /* Optimize loading states for mobile */
    .mobile-loading {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px;
    }
    
    .mobile-spinner {
        width: 30px;
        height: 30px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #8B4513;
        border-radius: 50%;
        animation: mobile-spin 1s linear infinite;
    }
    
    @keyframes mobile-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* Optimize error states for mobile */
    .mobile-error {
        padding: 20px;
        background: #ffebee;
        border: 1px solid #f44336;
        border-radius: 4px;
        color: #c62828;
        text-align: center;
        margin: 20px;
    }
    
    /* Optimize success states for mobile */
    .mobile-success {
        padding: 20px;
        background: #e8f5e8;
        border: 1px solid #4caf50;
        border-radius: 4px;
        color: #2e7d32;
        text-align: center;
        margin: 20px;
    }
}

/* Very small screens (phones in portrait) */
@media (max-width: 480px) {
    /* Further reduce complexity */
    * {
        animation-duration: 0.1s !important;
        transition-duration: 0.1s !important;
    }
    
    /* Optimize typography for small screens */
    .mobile-text {
        font-size: 14px;
        line-height: 1.5;
    }
    
    /* Optimize spacing for small screens */
    .mobile-content {
        padding: 10px;
    }
    
    .mobile-post-card {
        margin-bottom: 10px;
        padding: 10px;
    }
    
    /* Optimize navigation for small screens */
    .mobile-nav-item {
        padding: 8px;
        font-size: 12px;
    }
    
    /* Optimize forms for small screens */
    .mobile-form {
        padding: 15px;
    }
    
    .mobile-form input,
    .mobile-form textarea {
        padding: 12px;
        margin-bottom: 12px;
    }
    
    .mobile-form button {
        padding: 12px;
    }
}

/* Landscape orientation optimizations */
@media (max-width: 768px) and (orientation: landscape) {
    /* Optimize for landscape mode */
    .mobile-header {
        padding: 10px 15px;
    }
    
    .mobile-content {
        margin-top: 50px;
        margin-bottom: 60px;
    }
    
    .mobile-nav {
        padding: 8px;
    }
    
    .mobile-nav-item {
        padding: 8px;
    }
}

/* High DPI mobile displays */
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
    /* Optimize for retina displays */
    img {
        image-rendering: -webkit-optimize-contrast;
    }
    
    .mobile-text {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Slow connection optimizations */
@media (max-width: 768px) {
    /* Reduce visual complexity for slow connections */
    .slow-connection * {
        box-shadow: none !important;
        text-shadow: none !important;
        background-image: none !important;
        border-radius: 0 !important;
    }
    
    .slow-connection img {
        filter: none !important;
    }
    
    .slow-connection .mobile-post-card {
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
}

/* Battery saving mode */
@media (max-width: 768px) {
    .battery-save * {
        animation: none !important;
        transition: none !important;
        transform: none !important;
        filter: none !important;
        box-shadow: none !important;
    }
    
    .battery-save img {
        opacity: 0.8;
    }
    
    .battery-save .mobile-spinner {
        animation: none;
        border: 3px solid #ccc;
    }
}

/* Accessibility optimizations for mobile */
@media (max-width: 768px) {
    /* Larger touch targets for accessibility */
    .a11y-mobile button,
    .a11y-mobile .btn,
    .a11y-mobile a {
        min-height: 48px;
        min-width: 48px;
        padding: 12px;
    }
    
    /* High contrast mode */
    .high-contrast {
        filter: contrast(150%);
    }
    
    /* Large text mode */
    .large-text {
        font-size: 18px !important;
        line-height: 1.8 !important;
    }
    
    .large-text .mobile-nav-item {
        font-size: 16px !important;
    }
}

/* Print optimizations for mobile */
@media print and (max-width: 768px) {
    .mobile-nav,
    .mobile-header {
        display: none !important;
    }
    
    .mobile-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .mobile-post-card {
        break-inside: avoid;
        margin-bottom: 20px;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}
