/* CSS Variables for Naroop - Design System */

:root {
    /* ===== BRAND COLORS ===== */
    --color-primary: #ff6b35;
    --color-accent: #f7931e;
    --color-accent-secondary: #ffd700;
    
    /* ===== GRADIENTS ===== */
    --gradient-primary: linear-gradient(45deg, var(--color-primary), var(--color-accent), var(--color-accent-secondary));
    --gradient-dark: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
    --gradient-card: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
    --gradient-button: linear-gradient(45deg, var(--color-primary), var(--color-accent));
    --gradient-button-hover: linear-gradient(45deg, #e55a2b, #e0841a);
    
    /* ===== TEXT COLORS ===== */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.9);
    --text-muted: rgba(255, 255, 255, 0.8);
    --text-subtle: rgba(255, 255, 255, 0.75);
    --text-disabled: rgba(255, 255, 255, 0.5);
    
    /* ===== BACKGROUND COLORS ===== */
    --bg-primary: var(--gradient-dark);
    --bg-card: rgba(255, 255, 255, 0.05);
    --bg-card-hover: rgba(255, 107, 53, 0.1);
    --bg-input: rgba(255, 255, 255, 0.1);
    --bg-input-focus: rgba(255, 255, 255, 0.15);
    --bg-overlay: rgba(0, 0, 0, 0.8);
    
    /* ===== BORDER COLORS ===== */
    --border-primary: rgba(255, 255, 255, 0.1);
    --border-secondary: rgba(255, 255, 255, 0.2);
    --border-accent: var(--color-primary);
    --border-focus: rgba(255, 107, 53, 0.3);
    
    /* ===== SPACING SYSTEM ===== */
    --spacing-xs: 0.25rem;   /* 4px */
    --spacing-sm: 0.5rem;    /* 8px */
    --spacing-md: 1rem;      /* 16px */
    --spacing-lg: 1.5rem;    /* 24px */
    --spacing-xl: 2rem;      /* 32px */
    --spacing-2xl: 3rem;     /* 48px */
    --spacing-3xl: 4rem;     /* 64px */
    
    /* ===== BORDER RADIUS ===== */
    --radius-xs: 0.25rem;    /* 4px */
    --radius-sm: 0.5rem;     /* 8px */
    --radius-md: 0.75rem;    /* 12px */
    --radius-lg: 1rem;       /* 16px */
    --radius-xl: 1.25rem;    /* 20px */
    --radius-2xl: 1.5rem;    /* 24px */
    --radius-full: 50%;
    
    /* ===== TYPOGRAPHY ===== */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    /* Fluid Typography using clamp() */
    --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);      /* 12-14px */
    --font-size-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);        /* 14-16px */
    --font-size-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);        /* 16-18px */
    --font-size-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);       /* 18-20px */
    --font-size-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);        /* 20-24px */
    --font-size-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);             /* 24-32px */
    --font-size-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem);     /* 30-40px */
    
    /* Font Weights */
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    
    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.6;
    --line-height-loose: 1.8;
    
    /* ===== SHADOWS ===== */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 60px rgba(0, 0, 0, 0.3);
    --shadow-glow: 0 0 20px rgba(255, 107, 53, 0.3);
    --shadow-glow-strong: 0 0 30px rgba(255, 107, 53, 0.5);
    --color-shadow: rgba(0, 0, 0, 0.1);

    /* ===== LEGACY BORDER RADIUS (for compatibility) ===== */
    --border-radius-sm: var(--radius-sm);
    --border-radius-md: var(--radius-md);
    --border-radius-lg: var(--radius-lg);
    --border-radius-xl: var(--radius-xl);

    /* ===== LEGACY COLOR VARIABLES (for compatibility) ===== */
    --color-text: var(--text-primary);
    --color-background: var(--bg-primary);
    
    /* ===== Z-INDEX SCALE ===== */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
    
    /* ===== TRANSITIONS ===== */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    /* ===== BREAKPOINTS (for reference in JS) ===== */
    --breakpoint-xs: 320px;
    --breakpoint-sm: 480px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1200px;
    --breakpoint-2xl: 1400px;
    
    /* ===== COMPONENT SPECIFIC ===== */
    /* Header */
    --header-height: 80px;
    --header-bg: rgba(0, 0, 0, 0.8);
    --header-backdrop-blur: blur(20px);
    
    /* Sidebar */
    --sidebar-width: 300px;
    --sidebar-width-collapsed: 80px;
    
    /* Cards */
    --card-padding: var(--spacing-xl);
    --card-border: 1px solid var(--border-primary);
    --card-backdrop-blur: blur(10px);
    
    /* Buttons */
    --button-padding-sm: var(--spacing-sm) var(--spacing-md);
    --button-padding-md: var(--spacing-md) var(--spacing-lg);
    --button-padding-lg: var(--spacing-lg) var(--spacing-xl);
    
    /* Forms */
    --input-padding: var(--spacing-md) var(--spacing-lg);
    --input-border: 2px solid var(--border-secondary);
    --input-border-focus: 2px solid var(--border-accent);
    
    /* ===== ANIMATION CURVES ===== */
    --ease-in-quad: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    --ease-in-cubic: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    --ease-out-quad: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --ease-out-cubic: cubic-bezier(0.215, 0.61, 0.355, 1);
    --ease-in-out-quad: cubic-bezier(0.455, 0.03, 0.515, 0.955);
    --ease-in-out-cubic: cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* ===== DARK MODE OVERRIDES ===== */
@media (prefers-color-scheme: dark) {
    :root {
        /* Enhanced dark mode variables if needed */
        --text-muted: rgba(255, 255, 255, 0.85);
        --bg-card: rgba(255, 255, 255, 0.08);
    }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
    :root {
        --text-muted: rgba(255, 255, 255, 0.95);
        --text-subtle: rgba(255, 255, 255, 0.9);
        --border-primary: rgba(255, 255, 255, 0.3);
        --border-secondary: rgba(255, 255, 255, 0.4);
    }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
    :root {
        --transition-fast: 0.01ms;
        --transition-normal: 0.01ms;
        --transition-slow: 0.01ms;
        --transition-bounce: 0.01ms;
    }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* CSS Containment for better performance */
.card-base {
    contain: layout style;
}

.feed, .sidebar, .trending {
    contain: layout style paint;
}

/* Will-change for animated elements */
.nav-item,
.trending-item,
.create-post-btn {
    will-change: transform, background-color;
}

.nav-item:not(:hover),
.trending-item:not(:hover),
.create-post-btn:not(:hover) {
    will-change: auto;
}

/* GPU acceleration for smooth animations */
.card-base:hover,
.nav-item:hover,
.trending-item:hover,
.create-post-btn:hover {
    transform: translateZ(0);
}

/* Optimize font rendering */
body {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Optimize backdrop-filter performance */
.header,
.card-base {
    -webkit-backdrop-filter: var(--header-backdrop-blur);
    backdrop-filter: var(--header-backdrop-blur);
    transform: translateZ(0);
}
