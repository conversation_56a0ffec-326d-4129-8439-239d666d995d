<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect With The World</title>
    <link rel="stylesheet" href="./public/css/main.css">
    <link rel="stylesheet" href="./public/css/scroll-sections.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .cta-buttons {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
            margin-top: var(--spacing-lg);
        }

        .btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-decoration: none;
            font-size: 1rem;
        }

        .btn:hover {
            transform: translateY(-3px);
        }

        .btn-primary {
            background: linear-gradient(45deg, var(--color-primary), var(--color-accent));
            color: white;
            border: none;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid white;
            color: white;
        }

        .btn-accent {
            background: var(--color-accent);
            color: white;
            border: none;
        }

        .feature-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-xl);
        }

        .card {
            background: white;
            border-radius: var(--border-radius-md);
            padding: var(--spacing-lg);
            box-shadow: 0 4px 12px var(--color-shadow);
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px var(--color-shadow);
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
        }

        .community-showcase {
            margin-top: var(--spacing-xl);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-lg);
        }

        .community-stats {
            display: flex;
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-lg);
        }

        .stat {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--color-primary);
        }

        .stat-label {
            font-size: 1rem;
            color: #aaa;
        }
    </style>
</head>
<body>
    <div class="scroll-snap-container">
        <section class="section-gradient">
            <div class="section-content">
                <h1 class="section-heading">Welcome to Naroop</h1>
                <p class="section-subheading">Connect with friends, share moments, and discover communities.</p>
                <div class="cta-buttons">
                    <a href="./index.html" class="btn btn-primary">Get Started</a>
                    <button class="btn btn-outline opens-modal" data-modal-title="Learn More" data-modal-text="Naroop is a modern social platform designed to bring people together. Join us today!">Learn More</button>
                </div>
            </div>
        </section>
        
        <section class="section-light">
            <div class="section-content">
                <h2 class="section-heading">Share Your Story</h2>
                <p class="section-subheading">Create beautiful posts, photos, and videos to express yourself.</p>
                <div class="feature-cards">
                    <div class="card">
                        <div class="card-icon">📸</div>
                        <h3>Photo Sharing</h3>
                        <p>Share your favorite moments with crystal-clear photo uploads.</p>
                    </div>
                    <div class="card">
                        <div class="card-icon">🎬</div>
                        <h3>Video Posts</h3>
                        <p>Create and share engaging video content with your network.</p>
                    </div>
                    <div class="card">
                        <div class="card-icon">📝</div>
                        <h3>Blog Posts</h3>
                        <p>Express your thoughts with our rich text editor and formatting tools.</p>
                    </div>
                </div>
            </div>
        </section>
        
        <section class="section-dark">
            <div class="section-content">
                <h2 class="section-heading">Connect With Others</h2>
                <p class="section-subheading">Find communities that share your interests and passions.</p>
                <div class="community-showcase">
                    <div class="community-stats">
                        <div class="stat">
                            <span class="stat-number">10M+</span>
                            <span class="stat-label">Users</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">50K+</span>
                            <span class="stat-label">Communities</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">100M+</span>
                            <span class="stat-label">Posts</span>
                        </div>
                    </div>
                    <button class="btn btn-accent opens-modal" data-modal-title="Join the Community" data-modal-text="Sign up today and connect with millions of users sharing their passions and interests.">Join Now</button>
                </div>
            </div>
        </section>
        
        <section class="section-light">
            <div class="section-content">
                <h2 class="section-heading">Contact Us</h2>
                <p class="section-subheading">Have questions? We're here to help!</p>
                <button class="btn btn-primary opens-modal" data-modal-title="Contact Us" data-modal-text="Email <NAME_EMAIL> or call us at (555) 123-4567. We'll get back to you as soon as possible.">Contact Support</button>
            </div>
        </section>
    </div>
    
    <!-- Generic Modal Structure -->
    <div id="action-modal" class="modal-overlay" aria-hidden="true">
        <div class="modal-content" role="dialog" aria-modal="true">
            <div id="modal-body-content">
                <!-- Dynamic content will be loaded here by JavaScript -->
                <h2>Modal Title</h2>
                <p>This is placeholder content.</p>
            </div>
            <button class="modal-close-button" aria-label="Close modal">&times;</button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="./public/js/modal-system.js"></script>
    <script src="./public/js/scroll-sections.js"></script>
</body>
</html>
