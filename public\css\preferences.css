/* User Preferences and Accessibility Features */

/* ===== ACCESSIBILITY PREFERENCES ===== */

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --text-muted: rgba(255, 255, 255, 0.95);
        --text-subtle: rgba(255, 255, 255, 0.9);
        --border-primary: rgba(255, 255, 255, 0.4);
        --border-secondary: rgba(255, 255, 255, 0.5);
        --bg-card: rgba(255, 255, 255, 0.1);
    }
    
    .card-base {
        border-width: 2px;
    }
    
    .nav-item:focus,
    .create-post-btn:focus,
    .search-input:focus {
        outline: 3px solid var(--color-primary);
        outline-offset: 2px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .nav-item:hover,
    .trending-item:hover,
    .create-post-btn:hover {
        transform: none;
    }
}

/* ===== FOCUS MANAGEMENT ===== */

/* Enhanced focus indicators */
.nav-item:focus-visible,
.create-post-btn:focus-visible,
.search-input:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(255, 107, 53, 0.2);
}

/* Skip to content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--color-primary);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: var(--z-tooltip);
    transition: top var(--transition-fast);
}

.skip-link:focus {
    top: 6px;
}

/* ===== KEYBOARD NAVIGATION ===== */

/* Keyboard navigation indicators */
.nav-item[tabindex="0"]:focus,
.trending-item[tabindex="0"]:focus {
    background: var(--bg-card-hover);
    border-color: var(--border-accent);
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* ===== USER PREFERENCE CONTROLS ===== */

.preferences-panel {
    position: fixed;
    top: var(--header-height);
    right: -300px;
    width: 280px;
    height: calc(100vh - var(--header-height));
    background: var(--bg-card);
    border-left: var(--card-border);
    backdrop-filter: var(--card-backdrop-blur);
    padding: var(--spacing-xl);
    transition: right var(--transition-normal);
    z-index: var(--z-modal);
    overflow-y: auto;
}

.preferences-panel.open {
    right: 0;
}

.preferences-panel h3 {
    color: var(--color-primary);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
}

.preference-group {
    margin-bottom: var(--spacing-xl);
}

.preference-group h4 {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
}

/* Toggle Switch Component */
.toggle-switch {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-input);
    border-radius: var(--radius-md);
    border: var(--input-border);
    transition: var(--transition-normal);
}

.toggle-switch:hover {
    background: var(--bg-input-focus);
}

.toggle-switch label {
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    flex: 1;
}

.toggle-switch input[type="checkbox"] {
    position: relative;
    width: 44px;
    height: 24px;
    appearance: none;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition-normal);
}

.toggle-switch input[type="checkbox"]:checked {
    background: var(--color-primary);
}

.toggle-switch input[type="checkbox"]::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: var(--transition-normal);
}

.toggle-switch input[type="checkbox"]:checked::before {
    transform: translateX(20px);
}

.toggle-switch input[type="checkbox"]:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* Preferences Button */
.preferences-btn {
    background: transparent;
    border: var(--input-border);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: var(--font-size-lg);
}

.preferences-btn:hover {
    background: var(--bg-card-hover);
    border-color: var(--border-accent);
}

.preferences-btn:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* ===== LOADING STATES ===== */

.loading-skeleton {
    background: linear-gradient(
        90deg,
        var(--bg-card) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        var(--bg-card) 75%
    );
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
    border-radius: var(--radius-md);
}

@keyframes loading-shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.loading-skeleton.text {
    height: 1em;
    margin-bottom: var(--spacing-sm);
}

.loading-skeleton.text.short {
    width: 60%;
}

.loading-skeleton.text.medium {
    width: 80%;
}

.loading-skeleton.text.long {
    width: 100%;
}

.loading-skeleton.avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
}

.loading-skeleton.card {
    height: 120px;
    margin-bottom: var(--spacing-lg);
}

/* ===== MICRO-INTERACTIONS ===== */

/* Pulse animation for notifications */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

.notification-pulse {
    animation: pulse 2s infinite;
}

/* Bounce animation for buttons */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.bounce-on-click {
    animation: bounce 0.6s ease-in-out;
}

/* Fade in animation for content */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

/* Respect reduced motion for animations */
@media (prefers-reduced-motion: reduce) {
    .loading-skeleton,
    .notification-pulse,
    .bounce-on-click,
    .fade-in-up {
        animation: none;
    }
}

/* ===== ACCESSIBILITY MODE CLASSES ===== */

/* High Contrast Mode */
body.high-contrast-mode {
    --text-muted: rgba(255, 255, 255, 0.95);
    --text-subtle: rgba(255, 255, 255, 0.9);
    --border-primary: rgba(255, 255, 255, 0.4);
    --border-secondary: rgba(255, 255, 255, 0.5);
    --bg-card: rgba(255, 255, 255, 0.1);
}

body.high-contrast-mode .card-base {
    border-width: 2px;
}

body.high-contrast-mode .nav-item:focus,
body.high-contrast-mode .create-post-btn:focus,
body.high-contrast-mode .search-input:focus {
    outline: 3px solid var(--color-primary);
    outline-offset: 2px;
}

/* Large Text Mode */
body.large-text-mode {
    --font-size-xs: 0.875rem;
    --font-size-sm: 1rem;
    --font-size-base: 1.125rem;
    --font-size-lg: 1.25rem;
    --font-size-xl: 1.5rem;
    --font-size-2xl: 1.75rem;
    --font-size-3xl: 2rem;
    --font-size-4xl: 2.5rem;
}

body.large-text-mode .logo {
    font-size: 2rem;
}

body.large-text-mode .nav-item {
    font-size: 1.125rem;
    padding: var(--spacing-md) var(--spacing-lg);
}

/* Reduce Motion Mode */
body.reduce-motion-mode *,
body.reduce-motion-mode *::before,
body.reduce-motion-mode *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
}

body.reduce-motion-mode .nav-item:hover,
body.reduce-motion-mode .trending-item:hover,
body.reduce-motion-mode .create-post-btn:hover {
    transform: none;
}

body.reduce-motion-mode .loading-skeleton,
body.reduce-motion-mode .notification-pulse,
body.reduce-motion-mode .bounce-on-click,
body.reduce-motion-mode .fade-in-up {
    animation: none;
}

/* Enhanced Focus Mode */
body.enhanced-focus-mode .nav-item:focus-visible,
body.enhanced-focus-mode .create-post-btn:focus-visible,
body.enhanced-focus-mode .search-input:focus-visible,
body.enhanced-focus-mode .preferences-btn:focus-visible {
    outline: 3px solid var(--color-primary);
    outline-offset: 3px;
    box-shadow: 0 0 0 6px rgba(255, 107, 53, 0.3);
}

body.enhanced-focus-mode .nav-item[tabindex="0"]:focus,
body.enhanced-focus-mode .trending-item[tabindex="0"]:focus {
    background: var(--bg-card-hover);
    border-color: var(--border-accent);
    outline: 3px solid var(--color-primary);
    outline-offset: 3px;
    box-shadow: 0 0 0 6px rgba(255, 107, 53, 0.3);
}

/* Preference description styling */
.preference-description {
    color: var(--text-muted);
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
    line-height: 1.4;
}
