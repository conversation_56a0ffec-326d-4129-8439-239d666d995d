// Test Error Handling System for Naroop

import { errorHandler } from './error-handler.js';
import { formValidator } from './form-validator.js';

export class ErrorHandlingTests {
    constructor() {
        this.testResults = [];
    }

    // Run all tests
    async runTests() {
        console.log('🧪 Running Error Handling Tests...');
        
        try {
            await this.testNotifications();
            await this.testFormValidation();
            await this.testApiErrorHandling();
            await this.testLoadingStates();
            
            this.showTestResults();
        } catch (error) {
            console.error('Test execution failed:', error);
        }
    }

    // Test notification system
    async testNotifications() {
        console.log('Testing notifications...');
        
        // Test success notification
        errorHandler.showNotification('Test success notification', 'success', 2000);
        await this.wait(500);
        
        // Test error notification
        errorHandler.showNotification('Test error notification', 'error', 2000);
        await this.wait(500);
        
        // Test warning notification
        errorHandler.showNotification('Test warning notification', 'warning', 2000);
        await this.wait(500);
        
        // Test info notification
        errorHandler.showNotification('Test info notification', 'info', 2000);
        await this.wait(500);
        
        // Test loading notification
        errorHandler.showNotification('Test loading notification', 'loading', 2000);
        
        this.testResults.push({
            test: 'Notifications',
            status: 'PASS',
            message: 'All notification types displayed successfully'
        });
    }

    // Test form validation
    async testFormValidation() {
        console.log('Testing form validation...');
        
        try {
            // Create test form
            const testForm = document.createElement('form');
            testForm.innerHTML = `
                <input type="text" name="testField" data-validate="email" required>
                <button type="submit">Submit</button>
            `;
            testForm.style.display = 'none';
            document.body.appendChild(testForm);
            
            // Test validation
            const input = testForm.querySelector('input');
            
            // Test invalid email
            input.value = 'invalid-email';
            const isValid1 = formValidator.validateField(input);
            
            // Test valid email
            input.value = '<EMAIL>';
            const isValid2 = formValidator.validateField(input);
            
            // Clean up
            document.body.removeChild(testForm);
            
            if (!isValid1 && isValid2) {
                this.testResults.push({
                    test: 'Form Validation',
                    status: 'PASS',
                    message: 'Email validation working correctly'
                });
            } else {
                throw new Error('Validation logic failed');
            }
        } catch (error) {
            this.testResults.push({
                test: 'Form Validation',
                status: 'FAIL',
                message: error.message
            });
        }
    }

    // Test API error handling
    async testApiErrorHandling() {
        console.log('Testing API error handling...');
        
        try {
            // Simulate API error
            const mockError = new Error('Test API Error');
            mockError.status = 500;
            
            const result = await errorHandler.handleApiError(mockError, {
                method: 'GET',
                url: '/test-endpoint'
            });
            
            if (result.handled) {
                this.testResults.push({
                    test: 'API Error Handling',
                    status: 'PASS',
                    message: 'API errors handled correctly'
                });
            } else {
                throw new Error('API error not handled');
            }
        } catch (error) {
            this.testResults.push({
                test: 'API Error Handling',
                status: 'FAIL',
                message: error.message
            });
        }
    }

    // Test loading states
    async testLoadingStates() {
        console.log('Testing loading states...');
        
        try {
            // Create test button
            const testBtn = document.createElement('button');
            testBtn.textContent = 'Test Button';
            testBtn.style.display = 'none';
            document.body.appendChild(testBtn);
            
            // Test loading state
            const hideLoading = errorHandler.showLoading(testBtn, 'Testing...');
            
            if (testBtn.classList.contains('loading')) {
                hideLoading();
                
                if (!testBtn.classList.contains('loading')) {
                    this.testResults.push({
                        test: 'Loading States',
                        status: 'PASS',
                        message: 'Loading states working correctly'
                    });
                } else {
                    throw new Error('Loading state not cleared');
                }
            } else {
                throw new Error('Loading state not applied');
            }
            
            // Clean up
            document.body.removeChild(testBtn);
        } catch (error) {
            this.testResults.push({
                test: 'Loading States',
                status: 'FAIL',
                message: error.message
            });
        }
    }

    // Show test results
    showTestResults() {
        console.log('🧪 Error Handling Test Results:');
        console.table(this.testResults);
        
        const passCount = this.testResults.filter(r => r.status === 'PASS').length;
        const totalCount = this.testResults.length;
        
        const message = `Tests completed: ${passCount}/${totalCount} passed`;
        const type = passCount === totalCount ? 'success' : 'warning';
        
        errorHandler.showNotification(message, type, 5000);
        
        if (passCount === totalCount) {
            console.log('✅ All error handling tests passed!');
        } else {
            console.warn('⚠️ Some error handling tests failed');
        }
    }

    // Utility function to wait
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Create global test instance
export const errorHandlingTests = new ErrorHandlingTests();

// Make available globally for manual testing
window.ErrorHandlingTests = errorHandlingTests;

// Auto-run tests in development
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    document.addEventListener('DOMContentLoaded', () => {
        // Run tests after a delay to ensure everything is loaded
        setTimeout(() => {
            if (window.location.search.includes('test=true')) {
                errorHandlingTests.runTests();
            }
        }, 2000);
    });
}
