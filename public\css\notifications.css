/* Notification and Error Handling Styles for Naroop */

/* Notification Container */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    pointer-events: none;
    max-width: 400px;
    width: 100%;
}

/* Notification Base */
.notification {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    margin-bottom: 12px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: auto;
    border-left: 4px solid #e5e7eb;
    overflow: hidden;
    max-width: 100%;
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification.hide {
    opacity: 0;
    transform: translateX(100%);
    margin-bottom: 0;
    max-height: 0;
    padding: 0;
}

/* Notification Types */
.notification-success {
    border-left-color: #10b981;
    background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.notification-error {
    border-left-color: #ef4444;
    background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.notification-warning {
    border-left-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.notification-info {
    border-left-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
}

.notification-loading {
    border-left-color: #8B4513;
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.05) 0%, #ffffff 100%);
}

/* Notification Content */
.notification-content {
    padding: 16px 20px;
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.notification-icon {
    font-size: 16px;
    margin-right: 8px;
}

.notification-time {
    font-size: 11px;
    color: #6b7280;
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #374151;
}

.notification-message {
    color: #374151;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 8px;
}

.notification-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

.retry-btn {
    background: #8B4513;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.retry-btn:hover {
    background: #7a3c0f;
    transform: translateY(-1px);
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #8B4513;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button Loading State */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading .loading-spinner {
    margin-right: 8px;
}

/* Form Field Error States */
.form-group input.error,
.form-group textarea.error {
    border-color: #ef4444;
    background: #fef2f2;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group input.error:focus,
.form-group textarea.error:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.field-error {
    color: #ef4444;
    font-size: 12px;
    margin-top: 6px;
    display: none;
    font-weight: 500;
    animation: slideDown 0.3s ease-out;
}

.field-error.show {
    display: block;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Success Field State */
.form-group input.success,
.form-group textarea.success {
    border-color: #10b981;
    background: #f0fdf4;
}

.form-group input.success:focus,
.form-group textarea.success:focus {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-overlay-content {
    background: white;
    padding: 40px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 300px;
}

.loading-overlay .loading-spinner {
    width: 40px;
    height: 40px;
    border-width: 4px;
    margin: 0 auto 20px;
}

.loading-overlay-message {
    color: #374151;
    font-size: 16px;
    font-weight: 600;
}

/* Offline Indicator */
.offline-indicator {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #ef4444;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    z-index: 10000;
    box-shadow: 0 4px 20px rgba(239, 68, 68, 0.3);
    opacity: 0;
    transform: translateX(-50%) translateY(100%);
    transition: all 0.3s ease;
}

.offline-indicator.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .notification-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .notification {
        margin-bottom: 8px;
    }
    
    .notification-content {
        padding: 12px 16px;
    }
    
    .notification-message {
        font-size: 13px;
    }
    
    .loading-overlay-content {
        margin: 20px;
        padding: 30px 20px;
    }
    
    .offline-indicator {
        bottom: 80px; /* Above mobile navigation */
        left: 20px;
        right: 20px;
        transform: none;
        text-align: center;
    }
    
    .offline-indicator.show {
        transform: translateY(0);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .notification {
        border-width: 2px;
        border-style: solid;
    }
    
    .notification-success {
        border-color: #10b981;
    }
    
    .notification-error {
        border-color: #ef4444;
    }
    
    .notification-warning {
        border-color: #f59e0b;
    }
    
    .notification-info {
        border-color: #3b82f6;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .notification,
    .field-error,
    .loading-overlay,
    .offline-indicator {
        transition: none;
        animation: none;
    }
    
    .loading-spinner {
        animation: none;
        border-top-color: transparent;
    }
}

/* Dark Mode Support (Future) */
@media (prefers-color-scheme: dark) {
    .notification {
        background: #1f2937;
        color: #f9fafb;
    }
    
    .notification-message {
        color: #e5e7eb;
    }
    
    .notification-time {
        color: #9ca3af;
    }
    
    .loading-overlay-content {
        background: #1f2937;
        color: #f9fafb;
    }
    
    .loading-overlay-message {
        color: #e5e7eb;
    }
}
