// Mobile Performance Optimizer
// Specific optimizations for mobile devices and touch interactions

class MobileOptimizer {
    constructor() {
        this.isMobile = this.detectMobile();
        this.isSlowConnection = false;
        this.isBatterySaving = false;
        this.touchStartTime = 0;
        this.scrollTimeout = null;
        this.resizeTimeout = null;
        
        this.optimizations = {
            touchOptimization: true,
            scrollOptimization: true,
            imageOptimization: true,
            animationOptimization: true,
            networkOptimization: true,
            batteryOptimization: true
        };
        
        this.metrics = {
            touchLatency: [],
            scrollPerformance: [],
            batteryLevel: 1,
            connectionSpeed: 'unknown'
        };
    }

    // Initialize mobile optimizations
    async init() {
        if (!this.isMobile) {
            console.log('📱 Not a mobile device, skipping mobile optimizations');
            return;
        }
        
        try {
            console.log('📱 Initializing Mobile Optimizer...');
            
            // Detect connection quality
            await this.detectConnectionQuality();
            
            // Detect battery status
            await this.detectBatteryStatus();
            
            // Initialize touch optimizations
            this.initializeTouchOptimizations();
            
            // Initialize scroll optimizations
            this.initializeScrollOptimizations();
            
            // Initialize image optimizations
            this.initializeImageOptimizations();
            
            // Initialize animation optimizations
            this.initializeAnimationOptimizations();
            
            // Initialize network optimizations
            this.initializeNetworkOptimizations();
            
            // Initialize viewport optimizations
            this.initializeViewportOptimizations();
            
            // Apply mobile-specific CSS classes
            this.applyMobileClasses();
            
            console.log('✅ Mobile Optimizer initialized');
            
        } catch (error) {
            console.error('❌ Error initializing Mobile Optimizer:', error);
            throw error;
        }
    }

    // Detect if device is mobile
    detectMobile() {
        const userAgent = navigator.userAgent.toLowerCase();
        const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone'];
        
        // Check user agent
        const isMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword));
        
        // Check screen size
        const isMobileScreen = window.innerWidth <= 768;
        
        // Check touch capability
        const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        
        return isMobileUA || (isMobileScreen && hasTouch);
    }

    // Detect connection quality
    async detectConnectionQuality() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            this.metrics.connectionSpeed = connection.effectiveType;
            
            // Consider 2g and slow-2g as slow connections
            this.isSlowConnection = ['slow-2g', '2g'].includes(connection.effectiveType);
            
            if (this.isSlowConnection) {
                console.log('🐌 Slow connection detected, applying optimizations');
                document.body.classList.add('slow-connection');
            }
            
            // Listen for connection changes
            connection.addEventListener('change', () => {
                this.metrics.connectionSpeed = connection.effectiveType;
                this.isSlowConnection = ['slow-2g', '2g'].includes(connection.effectiveType);
                
                if (this.isSlowConnection) {
                    document.body.classList.add('slow-connection');
                } else {
                    document.body.classList.remove('slow-connection');
                }
            });
        }
    }

    // Detect battery status
    async detectBatteryStatus() {
        if ('getBattery' in navigator) {
            try {
                const battery = await navigator.getBattery();
                this.metrics.batteryLevel = battery.level;
                
                // Enable battery saving mode if battery is low
                this.isBatterySaving = battery.level < 0.2 || battery.charging === false;
                
                if (this.isBatterySaving) {
                    console.log('🔋 Low battery detected, enabling battery saving mode');
                    document.body.classList.add('battery-save');
                }
                
                // Listen for battery changes
                battery.addEventListener('levelchange', () => {
                    this.metrics.batteryLevel = battery.level;
                    this.isBatterySaving = battery.level < 0.2 || battery.charging === false;
                    
                    if (this.isBatterySaving) {
                        document.body.classList.add('battery-save');
                    } else {
                        document.body.classList.remove('battery-save');
                    }
                });
                
            } catch (error) {
                console.warn('Battery API not available:', error);
            }
        }
    }

    // Initialize touch optimizations
    initializeTouchOptimizations() {
        if (!this.optimizations.touchOptimization) return;
        
        // Add touch-action optimization to all interactive elements
        const interactiveElements = document.querySelectorAll('button, a, input, textarea, select, [onclick]');
        interactiveElements.forEach(element => {
            element.style.touchAction = 'manipulation';
            element.classList.add('touch-target');
        });
        
        // Optimize touch event handling
        document.addEventListener('touchstart', (e) => {
            this.touchStartTime = performance.now();
        }, { passive: true });
        
        document.addEventListener('touchend', (e) => {
            const touchLatency = performance.now() - this.touchStartTime;
            this.metrics.touchLatency.push(touchLatency);
            
            // Keep only last 10 measurements
            if (this.metrics.touchLatency.length > 10) {
                this.metrics.touchLatency.shift();
            }
        }, { passive: true });
        
        // Prevent 300ms click delay
        document.addEventListener('touchstart', function() {}, { passive: true });
        
        console.log('✅ Touch optimizations initialized');
    }

    // Initialize scroll optimizations
    initializeScrollOptimizations() {
        if (!this.optimizations.scrollOptimization) return;
        
        // Add smooth scrolling with momentum
        document.documentElement.style.webkitOverflowScrolling = 'touch';
        
        // Optimize scroll performance
        let isScrolling = false;
        
        document.addEventListener('scroll', () => {
            if (!isScrolling) {
                isScrolling = true;
                requestAnimationFrame(() => {
                    // Measure scroll performance
                    const scrollStart = performance.now();
                    
                    // Defer non-critical operations during scroll
                    if (this.scrollTimeout) {
                        clearTimeout(this.scrollTimeout);
                    }
                    
                    this.scrollTimeout = setTimeout(() => {
                        const scrollEnd = performance.now();
                        this.metrics.scrollPerformance.push(scrollEnd - scrollStart);
                        
                        // Keep only last 10 measurements
                        if (this.metrics.scrollPerformance.length > 10) {
                            this.metrics.scrollPerformance.shift();
                        }
                        
                        isScrolling = false;
                    }, 100);
                });
            }
        }, { passive: true });
        
        console.log('✅ Scroll optimizations initialized');
    }

    // Initialize image optimizations
    initializeImageOptimizations() {
        if (!this.optimizations.imageOptimization) return;
        
        // Optimize all images for mobile
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            // Add mobile-specific attributes
            img.loading = 'lazy';
            img.decoding = 'async';
            
            // Add mobile-specific classes
            img.classList.add('mobile-post-image');
            
            // Optimize image rendering
            img.style.imageRendering = '-webkit-optimize-contrast';
            
            // Add error handling
            img.addEventListener('error', () => {
                img.style.display = 'none';
            });
        });
        
        // Implement responsive images for mobile
        this.implementResponsiveImages();
        
        console.log('✅ Image optimizations initialized');
    }

    // Initialize animation optimizations
    initializeAnimationOptimizations() {
        if (!this.optimizations.animationOptimization) return;
        
        // Reduce animations on mobile for better performance
        const style = document.createElement('style');
        style.textContent = `
            @media (max-width: 768px) {
                * {
                    animation-duration: 0.2s !important;
                    transition-duration: 0.2s !important;
                }
            }
        `;
        document.head.appendChild(style);
        
        // Disable animations if battery is low or connection is slow
        if (this.isBatterySaving || this.isSlowConnection) {
            document.body.classList.add('reduce-motion');
        }
        
        console.log('✅ Animation optimizations initialized');
    }

    // Initialize network optimizations
    initializeNetworkOptimizations() {
        if (!this.optimizations.networkOptimization) return;
        
        // Implement request batching for mobile
        this.requestQueue = [];
        this.batchTimeout = null;
        
        // Override fetch for mobile optimization
        const originalFetch = window.fetch;
        window.fetch = (...args) => {
            if (this.isSlowConnection) {
                return this.batchRequest(...args);
            }
            return originalFetch(...args);
        };
        
        console.log('✅ Network optimizations initialized');
    }

    // Initialize viewport optimizations
    initializeViewportOptimizations() {
        // Set optimal viewport for mobile
        let viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            viewport = document.createElement('meta');
            viewport.name = 'viewport';
            document.head.appendChild(viewport);
        }
        
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover';
        
        // Handle orientation changes
        window.addEventListener('orientationchange', () => {
            if (this.resizeTimeout) {
                clearTimeout(this.resizeTimeout);
            }
            
            this.resizeTimeout = setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });
        
        console.log('✅ Viewport optimizations initialized');
    }

    // Apply mobile-specific CSS classes
    applyMobileClasses() {
        document.body.classList.add('mobile-optimized');
        
        // Add specific classes based on device characteristics
        if (this.isSlowConnection) {
            document.body.classList.add('slow-connection');
        }
        
        if (this.isBatterySaving) {
            document.body.classList.add('battery-save');
        }
        
        // Add classes to key elements
        const header = document.querySelector('.header');
        if (header) {
            header.classList.add('mobile-header');
        }
        
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.classList.add('mobile-content');
        }
        
        // Add mobile classes to post cards
        const postCards = document.querySelectorAll('.post-item');
        postCards.forEach(card => {
            card.classList.add('mobile-post-card', 'mobile-card');
        });
        
        // Add mobile classes to forms
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.classList.add('mobile-form');
        });
    }

    // Implement responsive images
    implementResponsiveImages() {
        const images = document.querySelectorAll('img[src]');
        images.forEach(img => {
            const src = img.src;
            
            // Create responsive image sources
            if (src && !src.includes('data:')) {
                // For mobile, prefer smaller images
                if (window.innerWidth <= 480) {
                    // Try to load a smaller version if available
                    const smallSrc = src.replace(/\.(jpg|jpeg|png)$/i, '_small.$1');
                    
                    // Test if small version exists
                    const testImg = new Image();
                    testImg.onload = () => {
                        img.src = smallSrc;
                    };
                    testImg.onerror = () => {
                        // Keep original if small version doesn't exist
                    };
                    testImg.src = smallSrc;
                }
            }
        });
    }

    // Handle orientation change
    handleOrientationChange() {
        // Recalculate viewport dimensions
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
        
        // Trigger layout recalculation
        document.body.style.height = '100vh';
        setTimeout(() => {
            document.body.style.height = '';
        }, 100);
    }

    // Batch requests for slow connections
    async batchRequest(...args) {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({ args, resolve, reject });
            
            if (this.batchTimeout) {
                clearTimeout(this.batchTimeout);
            }
            
            this.batchTimeout = setTimeout(() => {
                this.processBatchedRequests();
            }, 200); // 200ms delay for batching
        });
    }

    // Process batched requests
    async processBatchedRequests() {
        const batch = this.requestQueue.splice(0, 3); // Process 3 at a time
        
        const promises = batch.map(async ({ args, resolve, reject }) => {
            try {
                const response = await fetch(...args);
                resolve(response);
            } catch (error) {
                reject(error);
            }
        });
        
        await Promise.allSettled(promises);
        
        // Process remaining queue
        if (this.requestQueue.length > 0) {
            setTimeout(() => {
                this.processBatchedRequests();
            }, 100);
        }
    }

    // Get mobile performance metrics
    getMetrics() {
        const avgTouchLatency = this.metrics.touchLatency.length > 0 
            ? this.metrics.touchLatency.reduce((a, b) => a + b, 0) / this.metrics.touchLatency.length 
            : 0;
            
        const avgScrollPerformance = this.metrics.scrollPerformance.length > 0
            ? this.metrics.scrollPerformance.reduce((a, b) => a + b, 0) / this.metrics.scrollPerformance.length
            : 0;
        
        return {
            isMobile: this.isMobile,
            isSlowConnection: this.isSlowConnection,
            isBatterySaving: this.isBatterySaving,
            connectionSpeed: this.metrics.connectionSpeed,
            batteryLevel: this.metrics.batteryLevel,
            averageTouchLatency: avgTouchLatency,
            averageScrollPerformance: avgScrollPerformance,
            screenSize: {
                width: window.innerWidth,
                height: window.innerHeight,
                devicePixelRatio: window.devicePixelRatio
            }
        };
    }

    // Enable/disable specific optimizations
    toggleOptimization(type, enabled) {
        if (type in this.optimizations) {
            this.optimizations[type] = enabled;
            console.log(`${enabled ? '✅' : '❌'} ${type} ${enabled ? 'enabled' : 'disabled'}`);
        }
    }
}

// Create global instance
const mobileOptimizer = new MobileOptimizer();

// Make available globally
window.mobileOptimizer = mobileOptimizer;

// Export for module usage
export default mobileOptimizer;
