// Authentication management for Naroop
import { FirebaseAuth } from './firebase-config.js';
import { AppState, CoreUtils } from './core.js';

console.log('🔐 Loading authentication system...');

export const Authentication = {
    redirectInProgress: false,
    firebaseInitialized: false,

    // Initialize authentication
    async init() {
        console.log('🔐 Initializing Firebase authentication...');

        try {
            this.firebaseInitialized = await FirebaseAuth.init();
            if (this.firebaseInitialized) {
                console.log('✅ Firebase initialized successfully');
                // Only set authenticated if Firebase and local session both confirm
                const authState = await FirebaseAuth.getAuthState();
                const currentUser = localStorage.getItem('currentUser');
                const authToken = localStorage.getItem('authToken');
                if (authState.authenticated && currentUser && authToken) {
                    this.handleAuthentication(true);
                } else {
                    // Not authenticated, clear any stale session
                    CoreUtils.clearUserSession();
                }
            } else {
                console.error('❌ Firebase initialization failed - authentication unavailable');
                this.handleAuthenticationFailure();
            }
        } catch (error) {
            console.error('❌ Firebase error:', error.message);
            this.handleAuthenticationFailure();
        }
    },

    // Handle authentication failure
    handleAuthenticationFailure() {
        if (window.ErrorHandler) {
            window.ErrorHandler.showError('Authentication service is unavailable. Please try again later.', 'error');
        } else {
            console.error('Authentication service is unavailable. Please try again later.');
        }

        // Redirect after a short delay to allow user to see the message
        setTimeout(() => {
            window.location.href = '/landing.html';
        }, 3000);
    },

    // Handle authentication state
    handleAuthentication(firebaseAvailable) {
        console.log('🔐 Handling authentication, Firebase available:', firebaseAvailable);

        // Check for stored authentication
        const currentUser = localStorage.getItem('currentUser');
        const authToken = localStorage.getItem('authToken');

        if (currentUser && authToken) {
            console.log('✅ User is authenticated');
            AppState.currentUser = JSON.parse(currentUser);
            AppState.isAuthenticated = true;
            
            if (firebaseAvailable) {
                // Set up Firebase auth state monitoring
                FirebaseAuth.onAuthStateChanged((user) => {
                    if (!user && !this.redirectInProgress && AppState.isAuthenticated) {
                        // Only redirect if we were previously authenticated and now we're not
                        // This prevents unnecessary redirects during initial page load
                        this.redirectInProgress = true;
                        console.log('🔄 User signed out, redirecting...');
                        CoreUtils.clearUserSession();
                        setTimeout(() => {
                            window.location.href = '/landing.html';
                        }, 100);
                    } else if (user && !AppState.isAuthenticated) {
                        // User signed in, but our app state doesn't reflect this
                        // This can happen after a successful login
                        console.log('🔄 User signed in, updating app state...');
                        const currentUser = localStorage.getItem('currentUser');
                        const authToken = localStorage.getItem('authToken');
                        if (currentUser && authToken) {
                            AppState.currentUser = JSON.parse(currentUser);
                            AppState.isAuthenticated = true;
                            window.dispatchEvent(new Event('authChanged'));
                        }
                    }
                });
            }
        } else {
            console.log('❌ No authentication found, redirecting to landing');
            if (!this.redirectInProgress) {
                this.redirectInProgress = true;
                window.location.href = '/landing.html';
            }
        }
    },

    // Check authentication status
    async checkAuthStatus() {
        try {
            if (!this.firebaseInitialized) {
                return { authenticated: false, reason: 'Firebase not initialized' };
            }

            const authState = await FirebaseAuth.getAuthState();
            if (authState.authenticated) {
                AppState.currentUser = authState.user;
                AppState.isAuthenticated = true;
                return { authenticated: true, user: authState.user };
            } else {
                return { authenticated: false, reason: 'No valid session' };
            }
        } catch (error) {
            console.error('Error checking auth status:', error);
            return { authenticated: false, reason: 'Auth check failed', error: error.message };
        }
    },

    // Validate current session
    async validateSession() {
        try {
            if (!this.firebaseInitialized) {
                return { valid: false, reason: 'Firebase not initialized' };
            }

            const validation = await FirebaseAuth.validateSession();
            if (validation.valid) {
                AppState.currentUser = validation.user;
                AppState.isAuthenticated = true;
            }
            return validation;
        } catch (error) {
            console.error('Error validating session:', error);
            return { valid: false, reason: 'Validation failed', error: error.message };
        }
    },

    // Refresh authentication token
    async refreshToken() {
        try {
            if (!this.firebaseInitialized) {
                throw new Error('Firebase not initialized');
            }

            const newToken = await FirebaseAuth.refreshToken();
            if (newToken) {
                localStorage.setItem('authToken', newToken);
                console.log('✅ Token refreshed successfully');
                return newToken;
            } else {
                throw new Error('Token refresh failed');
            }
        } catch (error) {
            console.error('Error refreshing token:', error);
            return null;
        }
    },

    // Sign out user
    async signOut() {
        try {
            console.log('🚪 Starting user sign out process...');

            // Set flag to prevent auth state listeners from interfering
            this.redirectInProgress = true;

            if (this.firebaseInitialized) {
                const result = await FirebaseAuth.signOut();
                if (!result.success) {
                    console.warn('Firebase sign out had issues:', result.error);
                }
            }

            // Clear all local session data
            CoreUtils.clearUserSession();

            // Clear app state
            AppState.currentUser = null;
            AppState.isAuthenticated = false;

            // Dispatch auth changed event
            window.dispatchEvent(new Event('authChanged'));

            console.log('✅ User signed out successfully');

            // Small delay to ensure all cleanup is complete before redirect
            setTimeout(() => {
                window.location.href = '/landing.html';
            }, 100);

        } catch (error) {
            console.error('Error signing out:', error);

            // Force logout even if Firebase signout fails
            CoreUtils.clearUserSession();
            AppState.currentUser = null;
            AppState.isAuthenticated = false;
            window.dispatchEvent(new Event('authChanged'));

            setTimeout(() => {
                window.location.href = '/landing.html';
            }, 100);
        }
    },

    // Get current user
    getCurrentUser() {
        return AppState.currentUser;
    },

    // Check if user is authenticated
    isAuthenticated() {
        return AppState.isAuthenticated && AppState.currentUser !== null;
    },

    // Get authentication token
    async getAuthToken() {
        try {
            if (this.firebaseInitialized) {
                return await FirebaseAuth.getIdToken();
            } else {
                return localStorage.getItem('authToken');
            }
        } catch (error) {
            console.error('Error getting auth token:', error);
            return null;
        }
    },

    // Make authenticated API request
    async makeAuthenticatedRequest(url, options = {}) {
        try {
            const token = await this.getAuthToken();
            if (!token) {
                throw new Error('No authentication token available');
            }

            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
                ...options.headers
            };

            const response = await fetch(url, {
                ...options,
                headers
            });

            if (response.status === 401) {
                // Token expired or invalid, try to refresh
                const newToken = await this.refreshToken();
                if (newToken) {
                    // Retry with new token
                    headers.Authorization = `Bearer ${newToken}`;
                    return await fetch(url, { ...options, headers });
                } else {
                    // Refresh failed, redirect to login
                    this.signOut();
                    throw new Error('Authentication expired');
                }
            }

            return response;
        } catch (error) {
            console.error('Error making authenticated request:', error);
            throw error;
        }
    }
};

// Initialize authentication when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Initialize authentication after a short delay to ensure Firebase config is loaded
    setTimeout(() => {
        Authentication.init();
    }, 100);
});

// Make authentication available globally
window.Authentication = Authentication;
