// Scroll Section Navigation for Naroop 2.0

document.addEventListener('DOMContentLoaded', () => {
  const scrollContainer = document.querySelector('.scroll-snap-container');
  
  // If there's no scroll container on this page, exit early
  if (!scrollContainer) return;
  
  const sections = scrollContainer.querySelectorAll('section');
  const navContainer = document.createElement('div');
  navContainer.className = 'section-nav';
  
  // Create navigation dots
  sections.forEach((section, index) => {
    // Create nav dot
    const dot = document.createElement('div');
    dot.className = 'section-nav-dot';
    if (index === 0) dot.classList.add('active');
    
    // Add click event to scroll to corresponding section
    dot.addEventListener('click', () => {
      section.scrollIntoView({ behavior: 'smooth' });
    });
    
    navContainer.appendChild(dot);
  });
  
  // Add navigation dots to the page
  document.body.appendChild(navContainer);
  
  // Update active dot on scroll
  const observerOptions = {
    root: scrollContainer,
    rootMargin: '0px',
    threshold: 0.5 // When 50% of the section is visible
  };
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        // Get the index of the current section
        const currentIndex = Array.from(sections).indexOf(entry.target);
        
        // Update active dot
        const dots = navContainer.querySelectorAll('.section-nav-dot');
        dots.forEach((dot, index) => {
          if (index === currentIndex) {
            dot.classList.add('active');
          } else {
            dot.classList.remove('active');
          }
        });
      }
    });
  }, observerOptions);
  
  // Observe all sections
  sections.forEach(section => {
    observer.observe(section);
  });
  
  // Add keyboard navigation
  document.addEventListener('keydown', (e) => {
    // Find the currently active section
    const activeDot = navContainer.querySelector('.section-nav-dot.active');
    const dots = navContainer.querySelectorAll('.section-nav-dot');
    const currentIndex = Array.from(dots).indexOf(activeDot);
    
    if (e.key === 'ArrowDown' && currentIndex < sections.length - 1) {
      // Scroll to next section
      sections[currentIndex + 1].scrollIntoView({ behavior: 'smooth' });
    } else if (e.key === 'ArrowUp' && currentIndex > 0) {
      // Scroll to previous section
      sections[currentIndex - 1].scrollIntoView({ behavior: 'smooth' });
    }
  });
});
