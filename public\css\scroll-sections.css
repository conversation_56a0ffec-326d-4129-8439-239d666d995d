/* Scroll Snap Section Styles for Naroop 2.0 */

/* Import main CSS variables if this file is loaded separately */
@import url('../css/main.css');

/* Full-page section container with scroll snapping */
.scroll-snap-container {
    scroll-snap-type: y mandatory; /* Snap on the vertical axis */
    overflow-y: scroll;
    height: 100vh; /* Must have a defined height */
    scroll-behavior: smooth;
}

/* Apply this to the direct children sections */
.scroll-snap-container > section {
    scroll-snap-align: start; /* Snap the top of the section to the top of the viewport */
    height: 100vh;
    width: 100%;
    /* Add padding so content isn't at the very edge */
    padding: var(--spacing-xl); 
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Section styling */
.section-light {
    background-color: var(--color-light-gray);
    color: var(--color-text);
}

.section-dark {
    background-color: #333;
    color: var(--color-text-light);
}

.section-gradient {
    background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
    color: var(--color-text-light);
}

/* Section content */
.section-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
}

.section-heading {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
}

.section-subheading {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-xl);
    opacity: 0.8;
}

/* Section navigation dots */
.section-nav {
    position: fixed;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.section-nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.section-nav-dot.active {
    background-color: var(--color-primary);
    transform: scale(1.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .section-heading {
        font-size: 2rem;
    }
    
    .section-subheading {
        font-size: 1.2rem;
    }
    
    .scroll-snap-container > section {
        padding: var(--spacing-lg);
    }
}

/* Accessibility: Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .scroll-snap-container {
        scroll-snap-type: none;
        scroll-behavior: auto;
    }
}
