// Modern modal system for Naroop 2.0
// This file handles opening, closing, and managing content for the generic modal system

document.addEventListener('DOMContentLoaded', () => {
  const modalOverlay = document.getElementById('action-modal');
  const modalContent = document.getElementById('modal-body-content');
  const closeModalButton = document.querySelector('.modal-close-button');

  // Function to open the modal
  const openModal = (contentHTML) => {
    modalContent.innerHTML = contentHTML; // Set content for the modal
    modalOverlay.classList.add('active');
    modalOverlay.setAttribute('aria-hidden', 'false');
  };

  // Function to close the modal
  const closeModal = () => {
    modalOverlay.classList.remove('active');
    modalOverlay.setAttribute('aria-hidden', 'true');
  };

  // Attach event listeners to all buttons that should open the modal
  document.querySelectorAll('.opens-modal').forEach(button => {
    button.addEventListener('click', () => {
      // Get data attributes for modal content
      const title = button.getAttribute('data-modal-title') || 'Information';
      const text = button.getAttribute('data-modal-text') || 'Here is the requested information.';
      openModal(`<h2>${title}</h2><p>${text}</p>`);
    });
  });

  // Close modal events
  closeModalButton.addEventListener('click', closeModal);
  modalOverlay.addEventListener('click', (event) => {
    if (event.target === modalOverlay) {
      closeModal();
    }
  });

  // Export functions to be used by other modules
  window.ModalSystem = {
    open: openModal,
    close: closeModal
  };
});
