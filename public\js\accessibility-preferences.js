/**
 * Accessibility Preferences Manager
 * Handles user accessibility preferences and applies them dynamically
 */

class AccessibilityPreferences {
    constructor() {
        this.preferences = {
            highContrast: false,
            largeText: false,
            reduceMotion: false,
            enhancedFocus: false
        };
        
        this.init();
    }
    
    init() {
        this.loadPreferences();
        this.bindEvents();
        this.applyPreferences();
    }
    
    /**
     * Load preferences from localStorage
     */
    loadPreferences() {
        const saved = localStorage.getItem('naroop-accessibility-preferences');
        if (saved) {
            try {
                this.preferences = { ...this.preferences, ...JSON.parse(saved) };
            } catch (e) {
                console.warn('Failed to load accessibility preferences:', e);
            }
        }
        
        // Also check system preferences
        this.checkSystemPreferences();
    }
    
    /**
     * Check system accessibility preferences
     */
    checkSystemPreferences() {
        // Check for reduced motion preference
        if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            this.preferences.reduceMotion = true;
        }
        
        // Check for high contrast preference
        if (window.matchMedia && window.matchMedia('(prefers-contrast: high)').matches) {
            this.preferences.highContrast = true;
        }
        
        // Listen for changes in system preferences
        if (window.matchMedia) {
            window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
                this.preferences.reduceMotion = e.matches;
                this.applyPreferences();
                this.updateUI();
            });
            
            window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
                this.preferences.highContrast = e.matches;
                this.applyPreferences();
                this.updateUI();
            });
        }
    }
    
    /**
     * Save preferences to localStorage
     */
    savePreferences() {
        try {
            localStorage.setItem('naroop-accessibility-preferences', JSON.stringify(this.preferences));
        } catch (e) {
            console.warn('Failed to save accessibility preferences:', e);
        }
    }
    
    /**
     * Bind event listeners
     */
    bindEvents() {
        // Preferences panel toggle
        const preferencesBtn = document.getElementById('preferencesBtn');
        const preferencesPanel = document.getElementById('preferencesPanel');
        
        if (preferencesBtn && preferencesPanel) {
            preferencesBtn.addEventListener('click', () => this.togglePreferencesPanel());
            
            // Close panel when clicking outside
            document.addEventListener('click', (e) => {
                if (!preferencesPanel.contains(e.target) && !preferencesBtn.contains(e.target)) {
                    this.closePreferencesPanel();
                }
            });
            
            // Close panel on Escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && preferencesPanel.classList.contains('open')) {
                    this.closePreferencesPanel();
                    preferencesBtn.focus();
                }
            });
        }
        
        // Preference toggles
        const toggles = {
            'high-contrast': 'highContrast',
            'large-text': 'largeText',
            'reduce-motion': 'reduceMotion',
            'enhanced-focus': 'enhancedFocus'
        };
        
        Object.entries(toggles).forEach(([id, preference]) => {
            const toggle = document.getElementById(id);
            if (toggle) {
                toggle.addEventListener('change', (e) => {
                    this.preferences[preference] = e.target.checked;
                    this.applyPreferences();
                    this.savePreferences();
                });
            }
        });
    }
    
    /**
     * Toggle preferences panel visibility
     */
    togglePreferencesPanel() {
        const panel = document.getElementById('preferencesPanel');
        if (panel) {
            const isOpen = panel.classList.contains('open');
            if (isOpen) {
                this.closePreferencesPanel();
            } else {
                this.openPreferencesPanel();
            }
        }
    }
    
    /**
     * Open preferences panel
     */
    openPreferencesPanel() {
        const panel = document.getElementById('preferencesPanel');
        if (panel) {
            panel.classList.add('open');
            panel.setAttribute('aria-hidden', 'false');
            
            // Focus first interactive element
            const firstToggle = panel.querySelector('input[type="checkbox"]');
            if (firstToggle) {
                firstToggle.focus();
            }
            
            // Update UI to reflect current preferences
            this.updateUI();
        }
    }
    
    /**
     * Close preferences panel
     */
    closePreferencesPanel() {
        const panel = document.getElementById('preferencesPanel');
        if (panel) {
            panel.classList.remove('open');
            panel.setAttribute('aria-hidden', 'true');
        }
    }
    
    /**
     * Update UI to reflect current preferences
     */
    updateUI() {
        const toggles = {
            'high-contrast': 'highContrast',
            'large-text': 'largeText',
            'reduce-motion': 'reduceMotion',
            'enhanced-focus': 'enhancedFocus'
        };
        
        Object.entries(toggles).forEach(([id, preference]) => {
            const toggle = document.getElementById(id);
            if (toggle) {
                toggle.checked = this.preferences[preference];
            }
        });
    }
    
    /**
     * Apply accessibility preferences to the document
     */
    applyPreferences() {
        const body = document.body;
        
        // High Contrast Mode
        if (this.preferences.highContrast) {
            body.classList.add('high-contrast-mode');
        } else {
            body.classList.remove('high-contrast-mode');
        }
        
        // Large Text
        if (this.preferences.largeText) {
            body.classList.add('large-text-mode');
        } else {
            body.classList.remove('large-text-mode');
        }
        
        // Reduce Motion
        if (this.preferences.reduceMotion) {
            body.classList.add('reduce-motion-mode');
        } else {
            body.classList.remove('reduce-motion-mode');
        }
        
        // Enhanced Focus
        if (this.preferences.enhancedFocus) {
            body.classList.add('enhanced-focus-mode');
        } else {
            body.classList.remove('enhanced-focus-mode');
        }
    }
    
    /**
     * Get current preferences
     */
    getPreferences() {
        return { ...this.preferences };
    }
    
    /**
     * Set preferences programmatically
     */
    setPreferences(newPreferences) {
        this.preferences = { ...this.preferences, ...newPreferences };
        this.applyPreferences();
        this.updateUI();
        this.savePreferences();
    }
}

// Global functions for inline event handlers
window.closePreferences = function() {
    if (window.accessibilityPreferences) {
        window.accessibilityPreferences.closePreferencesPanel();
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.accessibilityPreferences = new AccessibilityPreferences();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AccessibilityPreferences;
}
