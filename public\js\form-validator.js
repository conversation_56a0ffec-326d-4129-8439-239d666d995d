// Enhanced Form Validation System for Naroop

import { errorHandler } from './error-handler.js';

export class FormValidator {
    constructor() {
        this.validationRules = new Map();
        this.activeValidations = new Map();
        this.debounceTimers = new Map();
        this.debounceDelay = 300; // ms
    }

    // Initialize form validation
    init() {
        this.setupDefaultRules();
        this.setupEventListeners();
        console.log('📝 Form validation system initialized');
    }

    // Setup default validation rules
    setupDefaultRules() {
        // Email validation
        this.addRule('email', {
            pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            message: 'Please enter a valid email address'
        });

        // Password validation
        this.addRule('password', {
            minLength: 6,
            pattern: /^(?=.*[a-zA-Z])(?=.*\d)/,
            message: 'Password must be at least 6 characters with letters and numbers'
        });

        // Username validation
        this.addRule('username', {
            minLength: 2,
            maxLength: 30,
            pattern: /^[a-zA-Z0-9_-]+$/,
            message: 'Username must be 2-30 characters, letters, numbers, underscore, or hyphen only'
        });

        // Post title validation
        this.addRule('postTitle', {
            minLength: 3,
            maxLength: 100,
            message: 'Title must be between 3 and 100 characters'
        });

        // Post content validation
        this.addRule('postContent', {
            minLength: 10,
            maxLength: 2000,
            message: 'Content must be between 10 and 2000 characters'
        });
    }

    // Add custom validation rule
    addRule(name, rule) {
        this.validationRules.set(name, rule);
    }

    // Setup event listeners for real-time validation
    setupEventListeners() {
        document.addEventListener('input', (e) => {
            if (e.target.matches('input, textarea')) {
                this.handleFieldInput(e.target);
            }
        });

        document.addEventListener('blur', (e) => {
            if (e.target.matches('input, textarea')) {
                this.validateField(e.target, true);
            }
        });

        document.addEventListener('submit', (e) => {
            if (e.target.matches('form[data-validate]')) {
                this.handleFormSubmit(e);
            }
        });
    }

    // Handle field input with debouncing
    handleFieldInput(field) {
        const fieldName = field.name || field.dataset.validate;
        if (!fieldName) return;

        // Clear existing timer
        if (this.debounceTimers.has(fieldName)) {
            clearTimeout(this.debounceTimers.get(fieldName));
        }

        // Set new timer
        const timer = setTimeout(() => {
            this.validateField(field, false);
            this.debounceTimers.delete(fieldName);
        }, this.debounceDelay);

        this.debounceTimers.set(fieldName, timer);
    }

    // Validate individual field
    validateField(field, showSuccess = false) {
        const fieldName = field.name || field.dataset.validate;
        const value = field.value.trim();
        
        // Get validation rules
        const rules = this.getFieldRules(field);
        if (!rules || rules.length === 0) return true;

        // Clear previous validation state
        this.clearFieldValidation(field);

        // Validate field
        const errors = [];
        let isValid = true;

        for (const rule of rules) {
            const result = this.validateRule(value, rule);
            if (!result.isValid) {
                errors.push(result.message);
                isValid = false;
                break; // Show only first error
            }
        }

        // Update field state
        if (!isValid) {
            this.showFieldError(field, errors[0]);
        } else if (showSuccess && value) {
            this.showFieldSuccess(field);
        }

        // Store validation result
        this.activeValidations.set(fieldName, { isValid, errors });

        return isValid;
    }

    // Get validation rules for field
    getFieldRules(field) {
        const rules = [];
        
        // Check for data-validate attribute
        const validateAttr = field.dataset.validate;
        if (validateAttr) {
            const ruleNames = validateAttr.split(' ');
            for (const ruleName of ruleNames) {
                const rule = this.validationRules.get(ruleName);
                if (rule) {
                    rules.push({ ...rule, required: field.required });
                }
            }
        }

        // Check for built-in HTML5 validation
        if (field.type === 'email' && !validateAttr) {
            rules.push(this.validationRules.get('email'));
        }

        // Add required rule if field is required
        if (field.required && !rules.some(r => r.required)) {
            rules.push({
                required: true,
                message: `${this.getFieldLabel(field)} is required`
            });
        }

        // Add custom rules from data attributes
        if (field.dataset.minLength) {
            rules.push({
                minLength: parseInt(field.dataset.minLength),
                message: field.dataset.minLengthMessage || 
                        `Minimum ${field.dataset.minLength} characters required`
            });
        }

        if (field.dataset.maxLength) {
            rules.push({
                maxLength: parseInt(field.dataset.maxLength),
                message: field.dataset.maxLengthMessage || 
                        `Maximum ${field.dataset.maxLength} characters allowed`
            });
        }

        if (field.dataset.pattern) {
            rules.push({
                pattern: new RegExp(field.dataset.pattern),
                message: field.dataset.patternMessage || 'Invalid format'
            });
        }

        return rules;
    }

    // Validate a single rule
    validateRule(value, rule) {
        // Required check
        if (rule.required && !value) {
            return {
                isValid: false,
                message: rule.message || 'This field is required'
            };
        }

        // Skip other validations if field is empty and not required
        if (!value && !rule.required) {
            return { isValid: true };
        }

        // Length validations
        if (rule.minLength && value.length < rule.minLength) {
            return {
                isValid: false,
                message: rule.message || `Minimum ${rule.minLength} characters required`
            };
        }

        if (rule.maxLength && value.length > rule.maxLength) {
            return {
                isValid: false,
                message: rule.message || `Maximum ${rule.maxLength} characters allowed`
            };
        }

        // Pattern validation
        if (rule.pattern && !rule.pattern.test(value)) {
            return {
                isValid: false,
                message: rule.message || 'Invalid format'
            };
        }

        // Custom validation function
        if (rule.custom && !rule.custom(value)) {
            return {
                isValid: false,
                message: rule.message || 'Invalid value'
            };
        }

        return { isValid: true };
    }

    // Validate entire form
    validateForm(form) {
        const fields = form.querySelectorAll('input, textarea, select');
        let isValid = true;
        const errors = {};

        for (const field of fields) {
            const fieldValid = this.validateField(field, true);
            if (!fieldValid) {
                isValid = false;
                const fieldName = field.name || field.dataset.validate;
                const validation = this.activeValidations.get(fieldName);
                if (validation) {
                    errors[fieldName] = validation.errors;
                }
            }
        }

        return { isValid, errors };
    }

    // Handle form submission
    handleFormSubmit(e) {
        e.preventDefault();
        
        const form = e.target;
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // Show loading state
        const hideLoading = errorHandler.showLoading(submitBtn, 'Validating...');
        
        setTimeout(() => {
            const validation = this.validateForm(form);
            hideLoading();
            
            if (validation.isValid) {
                // Form is valid, proceed with submission
                this.handleValidFormSubmit(form);
            } else {
                // Show validation errors
                const errorCount = Object.keys(validation.errors).length;
                errorHandler.showNotification(
                    `Please fix ${errorCount} error${errorCount > 1 ? 's' : ''} before submitting`,
                    'error',
                    5000
                );
                
                // Focus first invalid field
                const firstInvalidField = form.querySelector('.error');
                if (firstInvalidField) {
                    firstInvalidField.focus();
                }
            }
        }, 500); // Small delay to show loading state
    }

    // Handle valid form submission
    async handleValidFormSubmit(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        const action = form.action || form.dataset.action;
        const method = form.method || 'POST';

        try {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const hideLoading = errorHandler.showLoading(submitBtn, 'Submitting...');

            // Make API request
            const response = await fetch(action, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'include',
                body: JSON.stringify(data)
            });

            hideLoading();

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            // Show success message
            errorHandler.showNotification(
                result.message || 'Form submitted successfully!',
                'success',
                4000
            );

            // Reset form if specified
            if (form.dataset.resetOnSuccess !== 'false') {
                form.reset();
                this.clearFormValidation(form);
            }

            // Trigger custom event
            form.dispatchEvent(new CustomEvent('formSubmitSuccess', {
                detail: { data, result }
            }));

        } catch (error) {
            await errorHandler.handleApiError(error, {
                method: method,
                url: action
            }, () => this.handleValidFormSubmit(form));
        }
    }

    // Show field error
    showFieldError(field, message) {
        field.classList.remove('success');
        field.classList.add('error');
        
        let errorElement = field.parentNode.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            field.parentNode.appendChild(errorElement);
        }
        
        errorElement.textContent = message;
        errorElement.classList.add('show');
    }

    // Show field success
    showFieldSuccess(field) {
        field.classList.remove('error');
        field.classList.add('success');
        this.clearFieldError(field);
    }

    // Clear field validation
    clearFieldValidation(field) {
        field.classList.remove('error', 'success');
        this.clearFieldError(field);
    }

    // Clear field error
    clearFieldError(field) {
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.classList.remove('show');
        }
    }

    // Clear form validation
    clearFormValidation(form) {
        const fields = form.querySelectorAll('input, textarea, select');
        for (const field of fields) {
            this.clearFieldValidation(field);
        }
        this.activeValidations.clear();
    }

    // Get field label for error messages
    getFieldLabel(field) {
        const label = field.parentNode.querySelector('label');
        if (label) {
            return label.textContent.replace('*', '').trim();
        }
        return field.placeholder || field.name || 'Field';
    }

    // Add character counter to field
    addCharacterCounter(field, maxLength) {
        let counter = field.parentNode.querySelector('.char-counter');
        if (!counter) {
            counter = document.createElement('div');
            counter.className = 'char-counter';
            field.parentNode.appendChild(counter);
        }

        const updateCounter = () => {
            const current = field.value.length;
            const remaining = maxLength - current;
            counter.textContent = `${current}/${maxLength}`;
            
            if (remaining < 20) {
                counter.classList.add('warning');
            } else {
                counter.classList.remove('warning');
            }
            
            if (remaining < 0) {
                counter.classList.add('error');
            } else {
                counter.classList.remove('error');
            }
        };

        field.addEventListener('input', updateCounter);
        updateCounter();
    }
}

// Create global instance
export const formValidator = new FormValidator();

// Make available globally
window.FormValidator = formValidator;
