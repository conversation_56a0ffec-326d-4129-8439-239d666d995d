// Post management system for Naroop
import { AppState, CoreUtils } from './core.js';
import { Authentication } from './authentication.js';

console.log('📝 Loading posts system...');

// Import error handler if available
let errorHandler = null;
try {
    const module = await import('./error-handler.js');
    errorHandler = module.errorHandler;
} catch (error) {
    console.warn('Error handler not available:', error);
}

export const Posts = {
    postsContainer: null,
    emptyState: null,
    loadingState: null,

    // Initialize posts system
    init() {
        console.log('🔧 Initializing posts system...');
        this.postsContainer = document.getElementById('postsContainer');
        this.emptyState = document.getElementById('emptyState');
        this.loadingState = document.getElementById('postsLoading');
        
        this.setupEventListeners();
        this.loadPosts();
        console.log('✅ Posts system initialized');
    },

    // Setup event listeners
    setupEventListeners() {
        // Create post button
        const createPostBtn = document.getElementById('createPostBtn');
        if (createPostBtn) {
            createPostBtn.addEventListener('click', () => this.openCreatePostModal());
        }

        // Post form submission
        const postForm = document.getElementById('postForm');
        if (postForm) {
            postForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitPost();
            });
        }
    },

    // Load posts from server
    async loadPosts() {
        try {
            this.showLoading();

            const response = await fetch('/api/posts', {
                credentials: 'include',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (response.ok) {
                const posts = await response.json();
                AppState.currentPosts = posts;
                this.renderAllPosts();

                if (errorHandler && posts.length === 0) {
                    errorHandler.showNotification('No posts yet. Be the first to share your story!', 'info', 4000);
                }
            } else {
                const errorData = await response.json().catch(() => ({}));
                throw Object.assign(new Error(errorData.error || 'Failed to load posts'), {
                    status: response.status
                });
            }
        } catch (error) {
            console.error('Error loading posts:', error);

            if (errorHandler) {
                await errorHandler.handleApiError(error, {
                    method: 'GET',
                    url: '/api/posts'
                }, () => this.loadPosts());
            } else {
                this.showError('Failed to load posts. Please try again.');
            }
        } finally {
            this.hideLoading();
        }
    },

    // Render all posts
    renderAllPosts() {
        if (!this.postsContainer) return;

        this.postsContainer.innerHTML = '';
        
        if (AppState.currentPosts.length === 0) {
            this.showEmptyState();
        } else {
            this.hideEmptyState();
            AppState.currentPosts.forEach(post => {
                const postElement = this.createPostElement(post);
                this.postsContainer.appendChild(postElement);
            });
        }
    },

    // Create post element
    createPostElement(post) {
        const postDiv = document.createElement('div');
        postDiv.className = 'post-card';
        postDiv.dataset.postId = post.id;

        const timeAgo = CoreUtils.getTimeAgo(post.createdAt);
        const authorInitial = post.username ? post.username[0].toUpperCase() : 'U';

        postDiv.innerHTML = `
            <div class="post-header">
                <div class="post-author">
                    <div class="author-avatar">${authorInitial}</div>
                    <div class="author-info">
                        <div class="author-name">${CoreUtils.escapeHtml(post.username || 'Anonymous')}</div>
                        <div class="post-time">${timeAgo}</div>
                    </div>
                </div>
                <div class="post-menu">
                    <button class="post-menu-btn" data-action="menu" data-post-id="${post.id}">⋯</button>
                </div>
            </div>
            <div class="post-content">
                <h3 class="post-title">${CoreUtils.escapeHtml(post.title)}</h3>
                <p class="post-text">${CoreUtils.escapeHtml(post.content)}</p>
                ${post.image ? `<img src="${post.image}" alt="Post image" class="post-image">` : ''}
            </div>
            <div class="post-actions">
                <button class="action-btn like-btn ${post.isLiked ? 'liked' : ''}" data-action="like" data-post-id="${post.id}">
                    <span class="action-icon">❤️</span>
                    <span class="action-text">${post.likes || 0}</span>
                </button>
                <button class="action-btn comment-btn" data-action="comment" data-post-id="${post.id}">
                    <span class="action-icon">💬</span>
                    <span class="action-text">${post.comments || 0}</span>
                </button>
                <button class="action-btn share-btn" data-action="share" data-post-id="${post.id}">
                    <span class="action-icon">🔄</span>
                    <span class="action-text">${post.shares || 0}</span>
                </button>
            </div>
            <div class="post-stats">
                <span>${(post.likes || 0) + (post.comments || 0) + (post.shares || 0)} interactions</span>
            </div>
        `;

        return postDiv;
    },

    // Open create post modal
    openCreatePostModal() {
        // Check if the new modal system is available
        if (window.ModalSystem) {
            // Create form content for the modal
            const formContent = `
                <h2>Create New Post</h2>
                <form id="postForm" class="post-form">
                    <div class="form-group">
                        <label for="postTitle">Title</label>
                        <input type="text" id="postTitle" name="title" required>
                    </div>
                    <div class="form-group">
                        <label for="postContent">Content</label>
                        <textarea id="postContent" name="content" rows="4" required></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="cancel-btn" onclick="ModalSystem.close()">Cancel</button>
                        <button type="submit" class="submit-btn">Post</button>
                    </div>
                </form>
            `;
            
            window.ModalSystem.open(formContent);
            
            // Add event listener to the form
            setTimeout(() => {
                const postForm = document.getElementById('postForm');
                if (postForm) {
                    postForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.submitPost();
                        window.ModalSystem.close();
                    });
                }
            }, 100);
        } else {
            // Fallback to the old modal if the new system isn't loaded
            const modal = document.getElementById('postModal');
            if (modal) {
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        }
    },

    // Close create post modal (legacy method, kept for compatibility)
    closeCreatePostModal() {
        if (window.ModalSystem) {
            window.ModalSystem.close();
        } else {
            const modal = document.getElementById('postModal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }
    },

    // Submit new post (now handled by form validator)
    async submitPost() {
        try {
            const title = document.getElementById('postTitle')?.value?.trim();
            const content = document.getElementById('postContent')?.value?.trim();

            // Basic validation (form validator handles detailed validation)
            if (!title || !content) {
                if (errorHandler) {
                    errorHandler.showNotification('Please fill in both title and content', 'error', 4000);
                } else {
                    this.showError('Please fill in both title and content');
                }
                return;
            }

            if (!Authentication.isAuthenticated()) {
                if (errorHandler) {
                    errorHandler.showNotification('You must be logged in to create a post', 'error', 4000);
                } else {
                    this.showError('You must be logged in to create a post');
                }
                return;
            }

            const currentUser = Authentication.getCurrentUser();
            const postData = {
                title,
                content,
                userId: currentUser.uid || currentUser.id,
                username: currentUser.username
            };

            // Show loading state
            const submitBtn = document.querySelector('#postForm button[type="submit"]');
            let hideLoading = null;

            if (errorHandler) {
                hideLoading = errorHandler.showLoading(submitBtn, 'Creating post...');
            } else {
                this.showPostLoading();
            }

            const response = await Authentication.makeAuthenticatedRequest('/api/posts', {
                method: 'POST',
                body: JSON.stringify(postData)
            });

            if (hideLoading) hideLoading();

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    // Add new post to the beginning of the array
                    AppState.currentPosts.unshift(result.post);

                    // Re-render posts
                    this.renderAllPosts();

                    // Close modal and reset form
                    this.closeCreatePostModal();
                    this.resetPostForm();

                    // Show success notification
                    if (errorHandler) {
                        errorHandler.showNotification('Post created successfully! 🎉', 'success', 4000);
                    }

                    console.log('✅ Post created successfully');
                } else {
                    throw new Error(result.error || 'Failed to create post');
                }
            } else {
                const errorData = await response.json().catch(() => ({}));
                throw Object.assign(new Error(errorData.error || 'Failed to create post'), {
                    status: response.status
                });
            }
        } catch (error) {
            console.error('Error creating post:', error);

            if (errorHandler) {
                await errorHandler.handleApiError(error, {
                    method: 'POST',
                    url: '/api/posts'
                }, () => this.submitPost());
            } else {
                this.showError('Failed to create post. Please try again.');
            }
        } finally {
            if (!errorHandler) {
                this.hidePostLoading();
            }
        }
    },

    // Reset post form
    resetPostForm() {
        const titleInput = document.getElementById('postTitle');
        const contentInput = document.getElementById('postContent');
        
        if (titleInput) titleInput.value = '';
        if (contentInput) contentInput.value = '';
    },

    // Toggle like on post
    async toggleLike(postId) {
        try {
            if (!Authentication.isAuthenticated()) {
                this.showError('You must be logged in to like posts');
                return;
            }

            const currentUser = Authentication.getCurrentUser();
            const response = await Authentication.makeAuthenticatedRequest(`/api/posts/${postId}/like`, {
                method: 'POST',
                body: JSON.stringify({ userId: currentUser.uid || currentUser.id })
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    // Update post in local state
                    const post = AppState.currentPosts.find(p => p.id === postId);
                    if (post) {
                        post.likes = result.likes;
                        post.isLiked = result.liked;
                        
                        // Update UI
                        this.updatePostLikeUI(postId, result.likes, result.liked);
                    }
                }
            }
        } catch (error) {
            console.error('Error toggling like:', error);
        }
    },

    // Update post like UI
    updatePostLikeUI(postId, likes, isLiked) {
        const postElement = document.querySelector(`[data-post-id="${postId}"]`);
        if (postElement) {
            const likeBtn = postElement.querySelector('.like-btn');
            const likeText = postElement.querySelector('.like-btn .action-text');
            
            if (likeBtn) {
                likeBtn.classList.toggle('liked', isLiked);
            }
            if (likeText) {
                likeText.textContent = likes;
            }
        }
    },

    // Show comments (using modern modal)
    showComments(postId) {
        console.log(`Show comments for post: ${postId}`);
        
        // Find the post in our state
        const post = AppState.currentPosts.find(p => p.id === postId);
        
        if (window.ModalSystem) {
            const commentContent = `
                <h2>Comments on "${CoreUtils.escapeHtml(post?.title || 'Post')}"</h2>
                <div class="comments-section">
                    <p class="empty-comments">Comments feature coming soon!</p>
                    <div class="add-comment-form">
                        <textarea placeholder="Write a comment..." class="comment-input"></textarea>
                        <button class="comment-submit-btn">Post Comment</button>
                    </div>
                </div>
            `;
            
            window.ModalSystem.open(commentContent);
        } else {
            // Fallback for older browsers
            if (window.ErrorHandler) {
                window.ErrorHandler.showNotification('Comments feature coming soon!', 'info');
            } else {
                console.log('Comments feature coming soon!');
            }
        }
    },

    // Share post (using modern modal)
    sharePost(postId) {
        console.log(`Share post: ${postId}`);
        
        // Find the post in our state
        const post = AppState.currentPosts.find(p => p.id === postId);
        
        if (window.ModalSystem) {
            const shareContent = `
                <h2>Share This Post</h2>
                <div class="share-options">
                    <button class="share-btn facebook">Facebook</button>
                    <button class="share-btn twitter">Twitter</button>
                    <button class="share-btn whatsapp">WhatsApp</button>
                    <button class="share-btn email">Email</button>
                </div>
                <div class="share-link">
                    <input type="text" readonly value="https://naroop.com/post/${postId}" class="share-url-input">
                    <button class="copy-link-btn">Copy Link</button>
                </div>
            `;
            
            window.ModalSystem.open(shareContent);
            
            // Add event listener for copy button
            setTimeout(() => {
                const copyBtn = document.querySelector('.copy-link-btn');
                if (copyBtn) {
                    copyBtn.addEventListener('click', () => {
                        const input = document.querySelector('.share-url-input');
                        input.select();
                        document.execCommand('copy');
                        copyBtn.textContent = 'Copied!';
                        setTimeout(() => {
                            copyBtn.textContent = 'Copy Link';
                        }, 2000);
                    });
                }
            }, 100);
        } else {
            // Fallback for older browsers
            if (window.ErrorHandler) {
                window.ErrorHandler.showNotification('Share feature coming soon!', 'info');
            } else {
                console.log('Share feature coming soon!');
            }
        }
    },

    // Show post menu (using modern modal)
    showPostMenu(postId) {
        console.log(`Show menu for post: ${postId}`);
        
        if (window.ModalSystem) {
            const menuContent = `
                <h2>Post Options</h2>
                <div class="post-menu-options">
                    <button class="menu-option edit-post">Edit Post</button>
                    <button class="menu-option save-post">Save Post</button>
                    <button class="menu-option report-post">Report Post</button>
                    <button class="menu-option delete-post">Delete Post</button>
                </div>
            `;
            
            window.ModalSystem.open(menuContent);
        } else {
            // Fallback for older browsers
            if (window.ErrorHandler) {
                window.ErrorHandler.showNotification('Post menu coming soon!', 'info');
            } else {
                console.log('Post menu coming soon!');
            }
        }
    },

    // Show loading state
    showLoading() {
        if (this.loadingState) {
            this.loadingState.style.display = 'block';
        }
    },

    // Hide loading state
    hideLoading() {
        if (this.loadingState) {
            this.loadingState.style.display = 'none';
        }
    },

    // Show post loading
    showPostLoading() {
        const postLoading = document.getElementById('postLoading');
        if (postLoading) {
            postLoading.style.display = 'block';
        }
    },

    // Hide post loading
    hidePostLoading() {
        const postLoading = document.getElementById('postLoading');
        if (postLoading) {
            postLoading.style.display = 'none';
        }
    },

    // Show empty state
    showEmptyState() {
        if (!this.postsContainer) return;

        this.postsContainer.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-icon">📝</div>
                <h3>No stories yet</h3>
                <p>Be the first to share your positive experience with the community. Your story could inspire others!</p>
            </div>
        `;
    },

    // Hide empty state
    hideEmptyState() {
        const emptyState = this.postsContainer?.querySelector('.empty-state');
        if (emptyState) {
            emptyState.remove();
        }
    },

    // Show error
    showError(message) {
        CoreUtils.showError(message);
    }
};

// Initialize posts when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for other modules to initialize
    setTimeout(() => {
        Posts.init();

        // Delegated event listeners for post actions
        const postsContainer = document.getElementById('postsContainer');
        if (postsContainer) {
            postsContainer.addEventListener('click', function(e) {
                const btn = e.target.closest('button[data-action]');
                if (!btn) return;
                const action = btn.getAttribute('data-action');
                const postId = btn.getAttribute('data-post-id');
                if (!action || !postId) return;
                if (action === 'like') {
                    Posts.toggleLike(postId);
                } else if (action === 'comment') {
                    Posts.showComments(postId);
                } else if (action === 'share') {
                    Posts.sharePost(postId);
                } else if (action === 'menu') {
                    Posts.showPostMenu(postId);
                }
            });
        }
    }, 200);
});

// Make posts available globally
window.Posts = Posts;
