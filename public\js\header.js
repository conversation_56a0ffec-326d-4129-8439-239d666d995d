// Dynamically update header buttons based on authentication state
// Assumes AppState and CoreUtils are globally available

document.addEventListener('DOMContentLoaded', () => {
    const navButtons = document.querySelector('.nav-buttons');
    if (!navButtons) return;

    function renderHeaderButtons() {
        navButtons.innerHTML = '';
        if (window.AppState && window.AppState.isAuthenticated) {
            // Show only logout button
            const logoutBtn = document.createElement('button');
            logoutBtn.className = 'nav-btn primary';
            logoutBtn.textContent = 'Logout';
            logoutBtn.onclick = () => window.Authentication.signOut();
            navButtons.appendChild(logoutBtn);
        } else {
            // Show sign in and sign up buttons
            const signInBtn = document.createElement('button');
            signInBtn.className = 'nav-btn';
            signInBtn.textContent = 'Sign In';
            signInBtn.onclick = () => window.showSignIn && window.showSignIn();
            navButtons.appendChild(signInBtn);

            const signUpBtn = document.createElement('button');
            signUpBtn.className = 'nav-btn primary';
            signUpBtn.textContent = 'Sign Up';
            signUpBtn.onclick = () => window.showSignUp && window.showSignUp();
            navButtons.appendChild(signUpBtn);
        }
    }

    // Initial render
    renderHeaderButtons();

    // Listen for authentication changes
    window.addEventListener('authChanged', renderHeaderButtons);

    // Optionally, poll for changes if needed
    setInterval(() => {
        renderHeaderButtons();
    }, 1000);
});
