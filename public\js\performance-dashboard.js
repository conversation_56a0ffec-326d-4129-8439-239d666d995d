// Performance Monitoring Dashboard
// Real-time performance analytics and monitoring for Naroop platform

class PerformanceDashboard {
    constructor() {
        this.metrics = {
            realTime: {
                pageLoadTime: 0,
                domContentLoaded: 0,
                firstContentfulPaint: 0,
                largestContentfulPaint: 0,
                firstInputDelay: 0,
                cumulativeLayoutShift: 0,
                timeToInteractive: 0
            },
            network: {
                connectionType: 'unknown',
                effectiveType: 'unknown',
                downlink: 0,
                rtt: 0,
                saveData: false
            },
            resources: {
                totalResources: 0,
                totalSize: 0,
                cacheHits: 0,
                cacheMisses: 0,
                slowResources: []
            },
            errors: {
                jsErrors: 0,
                networkErrors: 0,
                lastError: null
            },
            user: {
                sessionDuration: 0,
                pageViews: 0,
                interactions: 0,
                bounceRate: 0
            }
        };
        
        this.observers = {
            performance: null,
            intersection: null,
            mutation: null
        };
        
        this.thresholds = {
            goodLCP: 2500,
            goodFID: 100,
            goodCLS: 0.1,
            slowResource: 1000
        };
        
        this.isInitialized = false;
        this.sessionStart = Date.now();
    }

    // Initialize performance monitoring
    async init() {
        try {
            console.log('📊 Initializing Performance Dashboard...');
            
            // Initialize Web Vitals monitoring
            this.initializeWebVitals();
            
            // Initialize network monitoring
            this.initializeNetworkMonitoring();
            
            // Initialize resource monitoring
            this.initializeResourceMonitoring();
            
            // Initialize error monitoring
            this.initializeErrorMonitoring();
            
            // Initialize user behavior tracking
            this.initializeUserTracking();
            
            // Start real-time monitoring
            this.startRealTimeMonitoring();
            
            // Create dashboard UI
            this.createDashboardUI();
            
            this.isInitialized = true;
            console.log('✅ Performance Dashboard initialized');
            
        } catch (error) {
            console.error('❌ Error initializing Performance Dashboard:', error);
            throw error;
        }
    }

    // Initialize Web Vitals monitoring
    initializeWebVitals() {
        // Monitor Largest Contentful Paint (LCP)
        if ('PerformanceObserver' in window) {
            try {
                const lcpObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    this.metrics.realTime.largestContentfulPaint = lastEntry.startTime;
                    this.updateDashboard();
                });
                lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
                
                // Monitor First Input Delay (FID)
                const fidObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        this.metrics.realTime.firstInputDelay = entry.processingStart - entry.startTime;
                        this.updateDashboard();
                    });
                });
                fidObserver.observe({ entryTypes: ['first-input'] });
                
                // Monitor Cumulative Layout Shift (CLS)
                let clsValue = 0;
                const clsObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (!entry.hadRecentInput) {
                            clsValue += entry.value;
                            this.metrics.realTime.cumulativeLayoutShift = clsValue;
                            this.updateDashboard();
                        }
                    });
                });
                clsObserver.observe({ entryTypes: ['layout-shift'] });
                
            } catch (error) {
                console.warn('Some Web Vitals metrics not supported:', error);
            }
        }
        
        // Monitor basic timing metrics
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                this.metrics.realTime.pageLoadTime = navigation.loadEventEnd - navigation.loadEventStart;
                this.metrics.realTime.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
                this.updateDashboard();
            }
        });
    }

    // Initialize network monitoring
    initializeNetworkMonitoring() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            this.metrics.network = {
                connectionType: connection.type || 'unknown',
                effectiveType: connection.effectiveType || 'unknown',
                downlink: connection.downlink || 0,
                rtt: connection.rtt || 0,
                saveData: connection.saveData || false
            };
            
            // Listen for connection changes
            connection.addEventListener('change', () => {
                this.metrics.network = {
                    connectionType: connection.type || 'unknown',
                    effectiveType: connection.effectiveType || 'unknown',
                    downlink: connection.downlink || 0,
                    rtt: connection.rtt || 0,
                    saveData: connection.saveData || false
                };
                this.updateDashboard();
            });
        }
    }

    // Initialize resource monitoring
    initializeResourceMonitoring() {
        if ('PerformanceObserver' in window) {
            const resourceObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    this.metrics.resources.totalResources++;
                    this.metrics.resources.totalSize += entry.transferSize || 0;
                    
                    // Check for slow resources
                    if (entry.duration > this.thresholds.slowResource) {
                        this.metrics.resources.slowResources.push({
                            name: entry.name,
                            duration: entry.duration,
                            size: entry.transferSize
                        });
                    }
                    
                    // Check cache status
                    if (entry.transferSize === 0 && entry.decodedBodySize > 0) {
                        this.metrics.resources.cacheHits++;
                    } else {
                        this.metrics.resources.cacheMisses++;
                    }
                });
                this.updateDashboard();
            });
            
            resourceObserver.observe({ entryTypes: ['resource'] });
        }
    }

    // Initialize error monitoring
    initializeErrorMonitoring() {
        // JavaScript errors
        window.addEventListener('error', (event) => {
            this.metrics.errors.jsErrors++;
            this.metrics.errors.lastError = {
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                timestamp: Date.now()
            };
            this.updateDashboard();
        });
        
        // Unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.metrics.errors.jsErrors++;
            this.metrics.errors.lastError = {
                type: 'promise',
                message: event.reason.toString(),
                timestamp: Date.now()
            };
            this.updateDashboard();
        });
        
        // Network errors (fetch failures)
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            try {
                const response = await originalFetch(...args);
                if (!response.ok) {
                    this.metrics.errors.networkErrors++;
                    this.metrics.errors.lastError = {
                        type: 'network',
                        message: `HTTP ${response.status}: ${response.statusText}`,
                        url: args[0],
                        timestamp: Date.now()
                    };
                    this.updateDashboard();
                }
                return response;
            } catch (error) {
                this.metrics.errors.networkErrors++;
                this.metrics.errors.lastError = {
                    type: 'network',
                    message: error.message,
                    url: args[0],
                    timestamp: Date.now()
                };
                this.updateDashboard();
                throw error;
            }
        };
    }

    // Initialize user behavior tracking
    initializeUserTracking() {
        // Track page views
        this.metrics.user.pageViews = 1;
        
        // Track interactions
        const interactionEvents = ['click', 'keydown', 'scroll', 'touchstart'];
        interactionEvents.forEach(event => {
            document.addEventListener(event, () => {
                this.metrics.user.interactions++;
            }, { passive: true });
        });
        
        // Track session duration
        setInterval(() => {
            this.metrics.user.sessionDuration = Date.now() - this.sessionStart;
        }, 1000);
        
        // Track bounce rate (simplified)
        setTimeout(() => {
            if (this.metrics.user.interactions < 2) {
                this.metrics.user.bounceRate = 1;
            } else {
                this.metrics.user.bounceRate = 0;
            }
        }, 30000); // 30 seconds
    }

    // Start real-time monitoring
    startRealTimeMonitoring() {
        setInterval(() => {
            this.collectRealTimeMetrics();
            this.updateDashboard();
        }, 5000); // Update every 5 seconds
    }

    // Collect real-time metrics
    collectRealTimeMetrics() {
        // Memory usage
        if ('memory' in performance) {
            this.metrics.memory = {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
            };
        }
        
        // Navigation timing
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            this.metrics.navigation = {
                redirectTime: navigation.redirectEnd - navigation.redirectStart,
                dnsTime: navigation.domainLookupEnd - navigation.domainLookupStart,
                connectTime: navigation.connectEnd - navigation.connectStart,
                requestTime: navigation.responseStart - navigation.requestStart,
                responseTime: navigation.responseEnd - navigation.responseStart,
                domProcessingTime: navigation.domContentLoadedEventStart - navigation.responseEnd,
                loadEventTime: navigation.loadEventEnd - navigation.loadEventStart
            };
        }
    }

    // Create dashboard UI
    createDashboardUI() {
        // Only create UI if in development mode or explicitly enabled
        if (window.location.hostname === 'localhost' || window.location.search.includes('debug=true')) {
            this.createDashboardElement();
        }
    }

    // Create dashboard DOM element
    createDashboardElement() {
        const dashboard = document.createElement('div');
        dashboard.id = 'performance-dashboard';
        dashboard.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            max-height: 400px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            overflow-y: auto;
            display: none;
        `;
        
        // Add toggle button
        const toggleBtn = document.createElement('button');
        toggleBtn.textContent = '📊';
        toggleBtn.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 40px;
            height: 40px;
            background: #8B4513;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            z-index: 10001;
            font-size: 16px;
        `;
        
        toggleBtn.addEventListener('click', () => {
            dashboard.style.display = dashboard.style.display === 'none' ? 'block' : 'none';
            toggleBtn.style.right = dashboard.style.display === 'none' ? '10px' : '320px';
        });
        
        document.body.appendChild(dashboard);
        document.body.appendChild(toggleBtn);
        
        this.dashboardElement = dashboard;
    }

    // Update dashboard display
    updateDashboard() {
        if (!this.dashboardElement) return;
        
        const formatTime = (ms) => `${ms.toFixed(2)}ms`;
        const formatBytes = (bytes) => {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        };
        
        this.dashboardElement.innerHTML = `
            <h3>Performance Dashboard</h3>
            
            <h4>Web Vitals</h4>
            <div>LCP: ${formatTime(this.metrics.realTime.largestContentfulPaint)} ${this.getVitalStatus('lcp')}</div>
            <div>FID: ${formatTime(this.metrics.realTime.firstInputDelay)} ${this.getVitalStatus('fid')}</div>
            <div>CLS: ${this.metrics.realTime.cumulativeLayoutShift.toFixed(3)} ${this.getVitalStatus('cls')}</div>
            
            <h4>Network</h4>
            <div>Type: ${this.metrics.network.effectiveType}</div>
            <div>Downlink: ${this.metrics.network.downlink} Mbps</div>
            <div>RTT: ${this.metrics.network.rtt}ms</div>
            
            <h4>Resources</h4>
            <div>Total: ${this.metrics.resources.totalResources}</div>
            <div>Size: ${formatBytes(this.metrics.resources.totalSize)}</div>
            <div>Cache Hits: ${this.metrics.resources.cacheHits}</div>
            <div>Slow Resources: ${this.metrics.resources.slowResources.length}</div>
            
            <h4>Errors</h4>
            <div>JS Errors: ${this.metrics.errors.jsErrors}</div>
            <div>Network Errors: ${this.metrics.errors.networkErrors}</div>
            
            <h4>User</h4>
            <div>Session: ${Math.floor(this.metrics.user.sessionDuration / 1000)}s</div>
            <div>Interactions: ${this.metrics.user.interactions}</div>
        `;
    }

    // Get vital status indicator
    getVitalStatus(vital) {
        const value = this.metrics.realTime[vital === 'lcp' ? 'largestContentfulPaint' : 
                                          vital === 'fid' ? 'firstInputDelay' : 
                                          'cumulativeLayoutShift'];
        
        if (vital === 'lcp') {
            return value <= this.thresholds.goodLCP ? '🟢' : value <= 4000 ? '🟡' : '🔴';
        } else if (vital === 'fid') {
            return value <= this.thresholds.goodFID ? '🟢' : value <= 300 ? '🟡' : '🔴';
        } else if (vital === 'cls') {
            return value <= this.thresholds.goodCLS ? '🟢' : value <= 0.25 ? '🟡' : '🔴';
        }
        
        return '';
    }

    // Get all metrics
    getMetrics() {
        return { ...this.metrics };
    }

    // Export metrics for analysis
    exportMetrics() {
        const exportData = {
            ...this.metrics,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `naroop-performance-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// Create global instance
const performanceDashboard = new PerformanceDashboard();

// Make available globally
window.performanceDashboard = performanceDashboard;

// Export for module usage
export default performanceDashboard;
